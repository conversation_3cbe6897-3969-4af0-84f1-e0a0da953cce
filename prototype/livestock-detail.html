<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牛001 - 详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .status-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .status-active { background: #dcfce7; color: #166534; }
        .hero-image {
            height: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .action-button {
            transition: all 0.2s ease;
        }
        .action-button:hover {
            transform: translateY(-1px);
        }
        .delete-button:hover {
            background-color: #dc2626 !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button class="p-2 -ml-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-900">牛001</h1>
            <button class="p-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-edit text-gray-600"></i>
            </button>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="pb-20">
        <!-- 牲畜照片 -->
        <div class="hero-image relative overflow-hidden">
            <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=400&h=250&fit=crop&crop=face" 
                 alt="牛001" class="w-full h-full object-cover">
            <div class="absolute top-4 right-4">
                <span class="status-badge status-active">存栏</span>
            </div>
        </div>

        <!-- 基础信息卡片 -->
        <div class="bg-white mx-4 -mt-6 rounded-xl shadow-sm border border-gray-100 relative z-10">
            <div class="p-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">牛001</h2>
                    <p class="text-gray-600">安格斯牛 · 公 · 2岁3个月</p>
                </div>

                <!-- 快速信息 -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-semibold text-gray-900">2岁</div>
                        <div class="text-sm text-gray-500">年龄</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-semibold text-gray-900">自繁</div>
                        <div class="text-sm text-gray-500">来源</div>
                    </div>
                    <div class="text-center p-3 bg-gray-50 rounded-lg">
                        <div class="text-lg font-semibold text-green-600">健康</div>
                        <div class="text-sm text-gray-500">状态</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">基础信息</h3>
                
                <div class="space-y-0">
                    <div class="info-item">
                        <span class="text-gray-600">唯一标识</span>
                        <span class="font-medium text-gray-900">牛001</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">物种</span>
                        <span class="font-medium text-gray-900">牛</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">品种</span>
                        <span class="font-medium text-gray-900">安格斯</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">性别</span>
                        <span class="font-medium text-gray-900">公</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">出生日期</span>
                        <span class="font-medium text-gray-900">2021年8月15日</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">当前年龄</span>
                        <span class="font-medium text-gray-900">2岁3个月</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">来源</span>
                        <span class="font-medium text-gray-900">自繁</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="text-gray-600">当前状态</span>
                        <span class="status-badge status-active">存栏</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 备注信息 -->
        <div class="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">备注信息</h3>
                <p class="text-gray-600 leading-relaxed">
                    这头牛生长状况良好，体重增长正常。定期进行健康检查，疫苗接种记录完整。适合继续饲养或作为种牛使用。
                </p>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mx-4 mt-6 space-y-3">
            <!-- 编辑按钮 -->
            <button class="action-button w-full bg-green-600 text-white py-4 rounded-xl font-medium hover:bg-green-700 transition-colors flex items-center justify-center">
                <i class="fas fa-edit mr-2"></i>
                编辑牲畜信息
            </button>
            
            <!-- 更多操作 -->
            <div class="grid grid-cols-2 gap-3">
                <button class="action-button bg-blue-600 text-white py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <i class="fas fa-share mr-2"></i>
                    分享
                </button>
                <button class="action-button bg-orange-600 text-white py-3 rounded-xl font-medium hover:bg-orange-700 transition-colors flex items-center justify-center">
                    <i class="fas fa-print mr-2"></i>
                    打印
                </button>
            </div>
            
            <!-- 删除按钮 -->
            <button class="delete-button action-button w-full bg-red-500 text-white py-4 rounded-xl font-medium transition-colors flex items-center justify-center" onclick="showDeleteConfirm()">
                <i class="fas fa-trash mr-2"></i>
                删除牲畜
            </button>
        </div>
    </main>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white mx-4 rounded-xl p-6 max-w-sm w-full">
            <div class="text-center">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">确认删除</h3>
                <p class="text-gray-600 mb-6">确定要删除牲畜"牛001"吗？此操作不可恢复。</p>
                
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors" onclick="hideDeleteConfirm()">
                        取消
                    </button>
                    <button class="flex-1 bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition-colors" onclick="confirmDelete()">
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        function showDeleteConfirm() {
            document.getElementById('deleteModal').classList.remove('hidden');
            document.getElementById('deleteModal').classList.add('flex');
        }

        function hideDeleteConfirm() {
            document.getElementById('deleteModal').classList.add('hidden');
            document.getElementById('deleteModal').classList.remove('flex');
        }

        function confirmDelete() {
            // 模拟删除操作
            alert('牲畜已删除');
            hideDeleteConfirm();
        }
    </script>
</body>
</html> 