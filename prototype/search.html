<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索牲畜</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .search-input {
            transition: all 0.2s ease;
        }
        .search-input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        .search-result {
            transition: all 0.2s ease;
        }
        .search-result:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .status-active { background: #dcfce7; color: #166534; }
        .status-for-sale { background: #dbeafe; color: #1d4ed8; }
        .status-sold { background: #f3f4f6; color: #6b7280; }
        .highlight {
            background-color: #fef3c7;
            padding: 1px 2px;
            border-radius: 2px;
        }
        .search-suggestion {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .search-suggestion:hover {
            background-color: #f9fafb;
        }
        .empty-state {
            animation: fadeIn 0.5s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 搜索导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center px-4 py-3">
            <button class="p-2 -ml-2 rounded-full hover:bg-gray-100 mr-2" onclick="goBack()">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <div class="flex-1 relative">
                <input type="text" 
                       id="searchInput"
                       class="search-input w-full pl-10 pr-10 py-2 bg-gray-100 border-0 rounded-full text-gray-900 placeholder-gray-500"
                       placeholder="搜索牲畜ID、品种..."
                       oninput="performSearch()"
                       onfocus="showSuggestions()"
                       onblur="hideSuggestions()">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 搜索建议 -->
    <div id="suggestions" class="bg-white border-b border-gray-200 hidden">
        <div class="px-4 py-2">
            <div class="text-sm text-gray-500 mb-2">热门搜索</div>
            <div class="space-y-1">
                <div class="search-suggestion px-3 py-2 rounded-lg flex items-center" onclick="searchFor('安格斯')">
                    <i class="fas fa-search text-gray-400 mr-3"></i>
                    <span class="text-gray-700">安格斯</span>
                </div>
                <div class="search-suggestion px-3 py-2 rounded-lg flex items-center" onclick="searchFor('牛')">
                    <i class="fas fa-search text-gray-400 mr-3"></i>
                    <span class="text-gray-700">牛</span>
                </div>
                <div class="search-suggestion px-3 py-2 rounded-lg flex items-center" onclick="searchFor('存栏')">
                    <i class="fas fa-search text-gray-400 mr-3"></i>
                    <span class="text-gray-700">存栏</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区 -->
    <main class="px-4 py-4 pb-20">
        <!-- 搜索结果 -->
        <div id="searchResults" class="hidden">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">搜索结果</h2>
                <span id="resultCount" class="text-sm text-gray-500">找到 3 个结果</span>
            </div>
            
            <div class="space-y-3">
                <!-- 搜索结果项 1 -->
                <div class="search-result bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=60&h=60&fit=crop&crop=face" 
                             alt="牛001" class="w-14 h-14 rounded-lg object-cover">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="font-semibold text-gray-900">牛001</h3>
                                <span class="status-badge status-active">存栏</span>
                            </div>
                            <div class="text-sm text-gray-600">
                                <div>物种: 牛 | 品种: <span class="highlight">安格斯</span></div>
                                <div>性别: 公 | 年龄: 2岁3个月</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 搜索结果项 2 -->
                <div class="search-result bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1548550023-2bdb3c5beed7?w=60&h=60&fit=crop&crop=face" 
                             alt="牛003" class="w-14 h-14 rounded-lg object-cover">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="font-semibold text-gray-900">牛003</h3>
                                <span class="status-badge status-active">存栏</span>
                            </div>
                            <div class="text-sm text-gray-600">
                                <div>物种: 牛 | 品种: 西门塔尔</div>
                                <div>性别: 公 | 年龄: 4岁6个月</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 搜索结果项 3 -->
                <div class="search-result bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=60&h=60&fit=crop&crop=face" 
                             alt="牛005" class="w-14 h-14 rounded-lg object-cover">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <h3 class="font-semibold text-gray-900">牛005</h3>
                                <span class="status-badge status-for-sale">待售</span>
                            </div>
                            <div class="text-sm text-gray-600">
                                <div>物种: 牛 | 品种: 黄牛</div>
                                <div>性别: 母 | 年龄: 3岁1个月</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 无搜索结果 -->
        <div id="noResults" class="empty-state text-center py-16 hidden">
            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-search text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">未找到相关牲畜</h3>
            <p class="text-gray-500 mb-6">试试搜索其他关键词，或检查拼写是否正确</p>
            <button class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors" onclick="clearSearch()">
                清除搜索
            </button>
        </div>

        <!-- 默认状态 -->
        <div id="defaultState" class="empty-state text-center py-16">
            <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-search text-green-600 text-3xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">搜索您的牲畜</h3>
            <p class="text-gray-500 mb-6">输入牲畜ID、品种或其他关键词来查找</p>
            
            <!-- 快速搜索标签 -->
            <div class="flex flex-wrap justify-center gap-2">
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" onclick="searchFor('牛')">
                    牛
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" onclick="searchFor('羊')">
                    羊
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" onclick="searchFor('猪')">
                    猪
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" onclick="searchFor('存栏')">
                    存栏
                </button>
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors" onclick="searchFor('待售')">
                    待售
                </button>
            </div>
        </div>
    </main>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        let searchTimeout;

        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            
            // 清除之前的搜索延时
            clearTimeout(searchTimeout);
            
            // 延时搜索，避免频繁触发
            searchTimeout = setTimeout(() => {
                if (query.length === 0) {
                    showDefaultState();
                } else if (query.length >= 1) {
                    // 模拟搜索
                    if (query.includes('安格斯') || query.includes('牛') || query.includes('001')) {
                        showSearchResults();
                    } else {
                        showNoResults();
                    }
                }
            }, 300);
        }

        function showSearchResults() {
            document.getElementById('defaultState').classList.add('hidden');
            document.getElementById('noResults').classList.add('hidden');
            document.getElementById('searchResults').classList.remove('hidden');
            document.getElementById('suggestions').classList.add('hidden');
        }

        function showNoResults() {
            document.getElementById('defaultState').classList.add('hidden');
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('noResults').classList.remove('hidden');
            document.getElementById('suggestions').classList.add('hidden');
        }

        function showDefaultState() {
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('noResults').classList.add('hidden');
            document.getElementById('defaultState').classList.remove('hidden');
            document.getElementById('suggestions').classList.add('hidden');
        }

        function showSuggestions() {
            const query = document.getElementById('searchInput').value.trim();
            if (query.length === 0) {
                document.getElementById('suggestions').classList.remove('hidden');
            }
        }

        function hideSuggestions() {
            setTimeout(() => {
                document.getElementById('suggestions').classList.add('hidden');
            }, 200);
        }

        function searchFor(term) {
            document.getElementById('searchInput').value = term;
            performSearch();
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            showDefaultState();
        }

        function goBack() {
            // 模拟返回操作
            window.history.back();
        }

        // 自动聚焦搜索框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('searchInput').focus();
        });
    </script>
</body>
</html> 