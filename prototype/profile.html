<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .profile-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .menu-item {
            transition: all 0.2s ease;
        }
        .menu-item:hover {
            background-color: #f9fafb;
        }
        .stats-card {
            transition: all 0.2s ease;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .add-button {
            position: relative;
            z-index: 10;
        }
        .add-button::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #10b981, #059669, #34d399);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .add-button:hover::before {
            opacity: 0.3;
        }
        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
            }
        }
        .add-button-icon {
            animation: pulse-glow 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 个人信息头部 -->
    <header class="profile-header text-white">
        <div class="px-4 py-8">
            <div class="flex items-center space-x-4">
                <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-3xl"></i>
                </div>
                <div class="flex-1">
                    <h1 class="text-xl font-bold mb-1">张三</h1>
                    <p class="text-green-100 text-sm">养殖户 · 使用天数: 128天</p>
                    <p class="text-green-100 text-sm">手机: 138****8888</p>
                </div>
                <button class="p-2 bg-white bg-opacity-20 rounded-lg">
                    <i class="fas fa-edit text-white"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="px-4 py-6 pb-24">
        <!-- 数据统计卡片 -->
        <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="stats-card bg-white rounded-xl p-4 text-center shadow-sm">
                <div class="text-2xl font-bold text-green-600 mb-1">36</div>
                <div class="text-xs text-gray-500">总牲畜数</div>
            </div>
            <div class="stats-card bg-white rounded-xl p-4 text-center shadow-sm">
                <div class="text-2xl font-bold text-blue-600 mb-1">24</div>
                <div class="text-xs text-gray-500">存栏数量</div>
            </div>
            <div class="stats-card bg-white rounded-xl p-4 text-center shadow-sm">
                <div class="text-2xl font-bold text-orange-600 mb-1">12</div>
                <div class="text-xs text-gray-500">本月新增</div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="bg-white rounded-xl shadow-sm mb-6">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">牧场管理</h3>
                
                <!-- 数据管理 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-database text-blue-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">数据备份</div>
                            <div class="text-sm text-gray-500">备份牲畜数据到云端</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 数据导出 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-download text-green-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">数据导出</div>
                            <div class="text-sm text-gray-500">导出Excel报表</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 统计报表 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-purple-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">统计报表</div>
                            <div class="text-sm text-gray-500">查看详细数据分析</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 设置菜单 -->
        <div class="bg-white rounded-xl shadow-sm mb-6">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">设置</h3>
                
                <!-- 通知设置 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bell text-yellow-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">通知设置</div>
                            <div class="text-sm text-gray-500">管理推送通知</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 隐私设置 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-shield-alt text-red-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">隐私设置</div>
                            <div class="text-sm text-gray-500">数据隐私保护</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 关于我们 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-info-circle text-gray-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">关于我们</div>
                            <div class="text-sm text-gray-500">版本 1.0.0</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 帮助与反馈 -->
        <div class="bg-white rounded-xl shadow-sm">
            <div class="p-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">帮助与反馈</h3>
                
                <!-- 使用帮助 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-question-circle text-indigo-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">使用帮助</div>
                            <div class="text-sm text-gray-500">查看使用教程</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 意见反馈 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-comment text-pink-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">意见反馈</div>
                            <div class="text-sm text-gray-500">提交建议和问题</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <!-- 联系客服 -->
                <div class="menu-item flex items-center justify-between py-3 px-2 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-headset text-teal-600"></i>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">联系客服</div>
                            <div class="text-sm text-gray-500">在线客服支持</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex items-center justify-around">
            <!-- Tab 1: 牲畜 -->
            <button class="flex flex-col items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors" onclick="goToLivestock()">
                <i class="fas fa-cow text-gray-400 text-xl mb-1"></i>
                <span class="text-xs font-medium text-gray-400">牲畜</span>
            </button>
            
            <!-- Tab 2: 添加 -->
            <button class="flex flex-col items-center py-1 px-3 rounded-lg hover:bg-gray-50 transition-all duration-200 add-button" onclick="goToAddPage()">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mb-1 shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-200 relative add-button-icon">
                    <i class="fas fa-plus text-white text-xl font-bold"></i>
                    <!-- 脉冲动画效果 -->
                    <div class="absolute inset-0 rounded-full bg-green-400 opacity-20 animate-ping"></div>
                </div>
                <span class="text-xs font-bold text-green-600">添加</span>
            </button>
            
            <!-- Tab 3: 我的 -->
            <button class="flex flex-col items-center py-2 px-3 rounded-lg bg-green-50 border border-green-200">
                <i class="fas fa-user text-green-600 text-xl mb-1"></i>
                <span class="text-xs font-medium text-green-600">我的</span>
            </button>
        </div>
    </nav>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        // 导航功能
        function goToLivestock() {
            alert('跳转到牲畜列表页面');
            // 实际开发中可以使用: window.location.href = 'livestock-list.html';
        }

        function goToAddPage() {
            alert('跳转到添加牲畜页面');
            // 实际开发中可以使用: window.location.href = 'add-livestock.html';
        }

        // 菜单项点击事件
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('.font-medium').textContent;
                alert(`打开${title}功能`);
            });
        });

        // 编辑个人信息
        document.querySelector('.fa-edit').parentElement.addEventListener('click', function() {
            alert('编辑个人信息');
        });
    </script>
</body>
</html> 