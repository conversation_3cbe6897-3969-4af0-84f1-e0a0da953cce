<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选牲畜</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .filter-option {
            transition: all 0.2s ease;
        }
        .filter-option.selected {
            background-color: #10b981;
            color: white;
            border-color: #10b981;
        }
        .filter-option:hover {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
        .filter-option.selected:hover {
            background-color: #059669;
        }
        .filter-section {
            margin-bottom: 24px;
        }
        .section-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            font-size: 16px;
        }
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }
        .active-filters {
            animation: slideIn 0.3s ease;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .filter-tag {
            background: #10b981;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            margin: 2px;
        }
        .filter-tag button {
            margin-left: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button class="p-2 -ml-2 rounded-full hover:bg-gray-100" onclick="closeFilter()">
                <i class="fas fa-times text-gray-600"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-900">筛选牲畜</h1>
            <button class="text-green-600 font-medium" onclick="resetFilters()">重置</button>
        </div>
    </header>

    <!-- 当前筛选条件 -->
    <div id="activeFilters" class="bg-white border-b border-gray-200 px-4 py-3 hidden">
        <div class="text-sm text-gray-600 mb-2">当前筛选条件:</div>
        <div id="filterTags" class="flex flex-wrap"></div>
    </div>

    <!-- 主内容区 -->
    <main class="px-4 py-6 pb-24">
        <!-- 物种筛选 -->
        <div class="filter-section">
            <h3 class="section-title">物种</h3>
            <div class="filter-grid">
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="species" data-value="all" onclick="selectFilter(this)">
                    全部
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="species" data-value="牛" onclick="selectFilter(this)">
                    牛
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="species" data-value="羊" onclick="selectFilter(this)">
                    羊
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="species" data-value="猪" onclick="selectFilter(this)">
                    猪
                </button>
            </div>
        </div>

        <!-- 性别筛选 -->
        <div class="filter-section">
            <h3 class="section-title">性别</h3>
            <div class="filter-grid">
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="gender" data-value="all" onclick="selectFilter(this)">
                    全部
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="gender" data-value="公" onclick="selectFilter(this)">
                    公
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="gender" data-value="母" onclick="selectFilter(this)">
                    母
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="gender" data-value="阉割" onclick="selectFilter(this)">
                    阉割
                </button>
            </div>
        </div>

        <!-- 状态筛选 -->
        <div class="filter-section">
            <h3 class="section-title">状态</h3>
            <div class="filter-grid">
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="status" data-value="all" onclick="selectFilter(this)">
                    全部
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="status" data-value="存栏" onclick="selectFilter(this)">
                    存栏
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="status" data-value="待售" onclick="selectFilter(this)">
                    待售
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="status" data-value="已售" onclick="selectFilter(this)">
                    已售
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="status" data-value="死亡" onclick="selectFilter(this)">
                    死亡
                </button>
            </div>
        </div>

        <!-- 年龄范围 -->
        <div class="filter-section">
            <h3 class="section-title">年龄范围</h3>
            <div class="filter-grid">
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="age" data-value="all" onclick="selectFilter(this)">
                    全部
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="age" data-value="0-1岁" onclick="selectFilter(this)">
                    0-1岁
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="age" data-value="1-3岁" onclick="selectFilter(this)">
                    1-3岁
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="age" data-value="3岁以上" onclick="selectFilter(this)">
                    3岁以上
                </button>
            </div>
        </div>

        <!-- 来源筛选 -->
        <div class="filter-section">
            <h3 class="section-title">来源</h3>
            <div class="filter-grid">
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="source" data-value="all" onclick="selectFilter(this)">
                    全部
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="source" data-value="自繁" onclick="selectFilter(this)">
                    自繁
                </button>
                <button class="filter-option border border-gray-300 rounded-lg py-3 px-4 text-center text-sm font-medium" 
                        data-filter="source" data-value="购入" onclick="selectFilter(this)">
                    购入
                </button>
            </div>
        </div>

        <!-- 快速筛选 -->
        <div class="filter-section">
            <h3 class="section-title">快速筛选</h3>
            <div class="space-y-2">
                <button class="w-full filter-option border border-gray-300 rounded-lg py-3 px-4 text-left text-sm font-medium flex items-center justify-between" 
                        onclick="quickFilter('healthy')">
                    <span>健康牲畜（存栏状态）</span>
                    <i class="fas fa-heart text-green-500"></i>
                </button>
                <button class="w-full filter-option border border-gray-300 rounded-lg py-3 px-4 text-left text-sm font-medium flex items-center justify-between" 
                        onclick="quickFilter('forSale')">
                    <span>可售牲畜（待售状态）</span>
                    <i class="fas fa-tag text-blue-500"></i>
                </button>
                <button class="w-full filter-option border border-gray-300 rounded-lg py-3 px-4 text-left text-sm font-medium flex items-center justify-between" 
                        onclick="quickFilter('young')">
                    <span>幼畜（1岁以下）</span>
                    <i class="fas fa-baby text-orange-500"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div class="flex space-x-3">
            <button class="flex-1 bg-gray-200 text-gray-800 py-3 rounded-xl font-medium hover:bg-gray-300 transition-colors" 
                    onclick="resetFilters()">
                重置筛选
            </button>
            <button class="flex-1 bg-green-600 text-white py-3 rounded-xl font-medium hover:bg-green-700 transition-colors" 
                    onclick="applyFilters()">
                <span id="applyButtonText">应用筛选</span>
            </button>
        </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        let selectedFilters = {
            species: 'all',
            gender: 'all',
            status: 'all',
            age: 'all',
            source: 'all'
        };

        function selectFilter(button) {
            const filterType = button.dataset.filter;
            const filterValue = button.dataset.value;
            
            // 移除同类型其他选项的选中状态
            const sameTypeButtons = document.querySelectorAll(`[data-filter="${filterType}"]`);
            sameTypeButtons.forEach(btn => btn.classList.remove('selected'));
            
            // 添加当前选项的选中状态
            button.classList.add('selected');
            
            // 更新筛选条件
            selectedFilters[filterType] = filterValue;
            
            updateActiveFilters();
            updateApplyButton();
        }

        function quickFilter(type) {
            // 重置所有筛选
            resetFilters();
            
            // 根据快速筛选类型设置条件
            switch(type) {
                case 'healthy':
                    selectFilterByValue('status', '存栏');
                    break;
                case 'forSale':
                    selectFilterByValue('status', '待售');
                    break;
                case 'young':
                    selectFilterByValue('age', '0-1岁');
                    break;
            }
        }

        function selectFilterByValue(filterType, value) {
            const button = document.querySelector(`[data-filter="${filterType}"][data-value="${value}"]`);
            if (button) {
                selectFilter(button);
            }
        }

        function updateActiveFilters() {
            const activeFiltersDiv = document.getElementById('activeFilters');
            const filterTagsDiv = document.getElementById('filterTags');
            
            // 清空现有标签
            filterTagsDiv.innerHTML = '';
            
            let hasActiveFilters = false;
            
            // 生成筛选标签
            Object.entries(selectedFilters).forEach(([type, value]) => {
                if (value !== 'all') {
                    hasActiveFilters = true;
                    const tag = document.createElement('span');
                    tag.className = 'filter-tag';
                    tag.innerHTML = `
                        ${getFilterTypeLabel(type)}: ${value}
                        <button onclick="removeFilter('${type}')">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    `;
                    filterTagsDiv.appendChild(tag);
                }
            });
            
            // 显示或隐藏活动筛选区域
            if (hasActiveFilters) {
                activeFiltersDiv.classList.remove('hidden');
                activeFiltersDiv.classList.add('active-filters');
            } else {
                activeFiltersDiv.classList.add('hidden');
            }
        }

        function getFilterTypeLabel(type) {
            const labels = {
                species: '物种',
                gender: '性别',
                status: '状态',
                age: '年龄',
                source: '来源'
            };
            return labels[type] || type;
        }

        function removeFilter(filterType) {
            selectedFilters[filterType] = 'all';
            
            // 更新UI
            const allButton = document.querySelector(`[data-filter="${filterType}"][data-value="all"]`);
            if (allButton) {
                selectFilter(allButton);
            }
        }

        function resetFilters() {
            // 重置所有筛选条件
            selectedFilters = {
                species: 'all',
                gender: 'all',
                status: 'all',
                age: 'all',
                source: 'all'
            };
            
            // 重置UI
            document.querySelectorAll('.filter-option').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 选中所有"全部"选项
            document.querySelectorAll('[data-value="all"]').forEach(btn => {
                btn.classList.add('selected');
            });
            
            updateActiveFilters();
            updateApplyButton();
        }

        function updateApplyButton() {
            const activeCount = Object.values(selectedFilters).filter(value => value !== 'all').length;
            const buttonText = document.getElementById('applyButtonText');
            
            if (activeCount > 0) {
                buttonText.textContent = `应用筛选 (${activeCount})`;
            } else {
                buttonText.textContent = '应用筛选';
            }
        }

        function applyFilters() {
            // 模拟应用筛选
            const activeCount = Object.values(selectedFilters).filter(value => value !== 'all').length;
            if (activeCount > 0) {
                alert(`已应用 ${activeCount} 个筛选条件`);
            } else {
                alert('已清除所有筛选条件');
            }
            closeFilter();
        }

        function closeFilter() {
            // 模拟关闭筛选面板
            window.history.back();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认选中所有"全部"选项
            document.querySelectorAll('[data-value="all"]').forEach(btn => {
                btn.classList.add('selected');
            });
        });
    </script>
</body>
</html>