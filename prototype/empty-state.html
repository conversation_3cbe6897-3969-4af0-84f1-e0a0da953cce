<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的牧场 - 空状态</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .empty-illustration {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .feature-card {
            transition: all 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .cta-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.2s ease;
        }
        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center">
                <i class="fas fa-cow text-green-600 text-xl mr-2"></i>
                <h1 class="text-lg font-semibold text-gray-900">我的牧场</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full hover:bg-gray-100 opacity-50 cursor-not-allowed">
                    <i class="fas fa-search text-gray-400"></i>
                </button>
                <button class="p-2 rounded-full hover:bg-gray-100 opacity-50 cursor-not-allowed">
                    <i class="fas fa-filter text-gray-400"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="px-4 py-8 pb-20">
        <!-- 空状态插图和文案 -->
        <div class="text-center mb-8">
            <div class="empty-illustration w-32 h-32 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fas fa-cow text-green-600 text-5xl"></i>
            </div>
            
            <h2 class="text-2xl font-bold text-gray-900 mb-3">欢迎来到您的牧场！</h2>
            <p class="text-gray-600 mb-2">您还没有添加任何牲畜哦</p>
            <p class="text-gray-500 text-sm mb-8">点击下方按钮，开始记录您的第一只牲畜吧</p>
            
            <!-- 主要操作按钮 -->
            <button class="cta-button w-full max-w-xs mx-auto text-white py-4 px-6 rounded-xl font-semibold text-lg flex items-center justify-center mb-6">
                <i class="fas fa-plus mr-2"></i>
                添加第一只牲畜
            </button>
        </div>

        <!-- 功能介绍卡片 -->
        <div class="space-y-4 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 text-center mb-4">牧场管理功能</h3>
            
            <!-- 功能卡片 1 -->
            <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clipboard-list text-blue-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">档案管理</h4>
                        <p class="text-sm text-gray-600">记录牲畜的详细信息，包括品种、年龄、健康状况等</p>
                    </div>
                </div>
            </div>

            <!-- 功能卡片 2 -->
            <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-search text-green-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">快速搜索</h4>
                        <p class="text-sm text-gray-600">通过ID、品种等关键词快速找到您要查看的牲畜</p>
                    </div>
                </div>
            </div>

            <!-- 功能卡片 3 -->
            <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-filter text-purple-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">智能筛选</h4>
                        <p class="text-sm text-gray-600">按物种、性别、状态等条件筛选，快速分类管理</p>
                    </div>
                </div>
            </div>

            <!-- 功能卡片 4 -->
            <div class="feature-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-orange-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">数据统计</h4>
                        <p class="text-sm text-gray-600">实时查看存栏、待售、已售等状态的统计数据</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用提示 -->
        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <i class="fas fa-lightbulb text-blue-600 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-900 mb-2">使用小贴士</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• 建议为每只牲畜拍照，便于识别和管理</li>
                        <li>• 使用统一的编号规则，如"牛001"、"羊001"等</li>
                        <li>• 定期更新牲畜状态，保持数据准确性</li>
                        <li>• 充分利用搜索和筛选功能提高管理效率</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <!-- 浮动添加按钮 -->
    <button class="fixed bottom-6 right-6 w-14 h-14 bg-green-600 rounded-full shadow-lg hover:bg-green-700 transition-all duration-200 flex items-center justify-center hover:scale-110">
        <i class="fas fa-plus text-white text-xl"></i>
    </button>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为功能卡片添加点击效果
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 模拟功能介绍
                    const title = this.querySelector('h4').textContent;
                    alert(`${title}功能即将推出，敬请期待！`);
                });
            });

            // 主要操作按钮点击事件
            document.querySelector('.cta-button').addEventListener('click', function() {
                alert('跳转到添加牲畜页面');
            });

            // 浮动按钮点击事件
            document.querySelector('.fixed button').addEventListener('click', function() {
                alert('跳转到添加牲畜页面');
            });
        });
    </script>
</body>
</html> 