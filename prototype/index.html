<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>牲畜管理App - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .prototype-container {
            width: 375px;
            height: 812px;
            border: 2px solid #333;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            background: #000;
            padding: 8px;
        }
        .prototype-screen {
            width: 100%;
            height: 100%;
            border-radius: 20px;
            overflow: hidden;
            background: white;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">牲畜管理App - 高保真原型</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 牲畜列表页 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">牲畜列表页（主页）</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="livestock-list.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 添加牲畜页 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">添加牲畜页</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="add-livestock.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 牲畜详情页 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">牲畜详情页</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="livestock-detail.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 搜索界面 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">搜索界面</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="search.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 筛选界面 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">筛选界面</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="filter.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 空状态页面 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">空状态页面</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="empty-state.html"></iframe>
                    </div>
                </div>
            </div>

            <!-- 个人中心页面 -->
            <div class="text-center">
                <h2 class="text-xl font-semibold mb-4 text-gray-700">个人中心页面</h2>
                <div class="prototype-container mx-auto">
                    <div class="prototype-screen">
                        <iframe src="profile.html"></iframe>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-12 text-center">
            <p class="text-gray-600">以上原型展示了牲畜管理App的核心界面，采用现代化设计风格，符合iOS/Android设计规范</p>
        </div>
    </div>
</body>
</html>