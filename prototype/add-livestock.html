<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加牲畜</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }
        .form-input:focus {
            outline: none;
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        .required::after {
            content: " *";
            color: #ef4444;
        }
        .photo-upload {
            width: 120px;
            height: 120px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: border-color 0.2s;
        }
        .photo-upload:hover {
            border-color: #10b981;
        }
        .conditional-fields {
            display: none;
            animation: fadeIn 0.3s ease;
        }
        .conditional-fields.show {
            display: block;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <button class="p-2 -ml-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-times text-gray-600"></i>
            </button>
            <h1 class="text-lg font-semibold text-gray-900">添加牲畜</h1>
            <button class="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors">
                保存
            </button>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="px-4 py-6 pb-20">
        <form class="space-y-6">
            <!-- 牲畜照片 -->
            <div class="form-group">
                <label class="form-label">牲畜照片</label>
                <div class="photo-upload bg-gray-50">
                    <i class="fas fa-camera text-gray-400 text-2xl mb-2"></i>
                    <span class="text-sm text-gray-500">点击添加照片</span>
                </div>
            </div>

            <!-- 唯一标识 -->
            <div class="form-group">
                <label class="form-label required">唯一标识</label>
                <input type="text" class="form-input" placeholder="请输入耳标号或自定义编号" required>
                <p class="text-sm text-gray-500 mt-1">例如：耳标号、自定义编号</p>
            </div>

            <!-- 物种 -->
            <div class="form-group">
                <label class="form-label required">物种</label>
                <select class="form-input" required>
                    <option value="">请选择物种</option>
                    <option value="牛">牛</option>
                    <option value="羊">羊</option>
                    <option value="猪">猪</option>
                </select>
            </div>

            <!-- 品种 -->
            <div class="form-group">
                <label class="form-label">品种</label>
                <input type="text" class="form-input" placeholder="请输入品种">
            </div>

            <!-- 性别 -->
            <div class="form-group">
                <label class="form-label required">性别</label>
                <select class="form-input" required>
                    <option value="">请选择性别</option>
                    <option value="公">公</option>
                    <option value="母">母</option>
                    <option value="阉割">阉割</option>
                </select>
            </div>

            <!-- 出生日期 -->
            <div class="form-group">
                <label class="form-label required">出生日期</label>
                <input type="date" class="form-input" required>
            </div>

            <!-- 来源 -->
            <div class="form-group">
                <label class="form-label required">来源</label>
                <select class="form-input" id="source" required onchange="togglePurchaseFields()">
                    <option value="">请选择来源</option>
                    <option value="自繁">自繁</option>
                    <option value="购入">购入</option>
                </select>
            </div>

            <!-- 购入信息（条件显示） -->
            <div id="purchaseFields" class="conditional-fields space-y-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-4">购入信息</h3>
                    
                    <!-- 购入日期 -->
                    <div class="form-group">
                        <label class="form-label">购入日期</label>
                        <input type="date" class="form-input">
                    </div>

                    <!-- 购入价格 -->
                    <div class="form-group">
                        <label class="form-label">购入价格</label>
                        <div class="relative">
                            <input type="number" class="form-input pr-12" placeholder="0.00" step="0.01">
                            <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-500">元</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 状态 -->
            <div class="form-group">
                <label class="form-label required">状态</label>
                <select class="form-input" required>
                    <option value="存栏" selected>存栏</option>
                    <option value="待售">待售</option>
                    <option value="已售">已售</option>
                    <option value="死亡">死亡</option>
                </select>
            </div>

            <!-- 备注 -->
            <div class="form-group">
                <label class="form-label">备注</label>
                <textarea class="form-input" rows="4" placeholder="请输入备注信息"></textarea>
            </div>

            <!-- 必填提示 -->
            <div class="text-sm text-gray-500 text-center">
                <i class="fas fa-info-circle mr-1"></i>
                标有 * 的为必填项
            </div>
        </form>
    </main>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>

    <script>
        function togglePurchaseFields() {
            const source = document.getElementById('source').value;
            const purchaseFields = document.getElementById('purchaseFields');
            
            if (source === '购入') {
                purchaseFields.classList.add('show');
            } else {
                purchaseFields.classList.remove('show');
            }
        }

        // 照片上传模拟
        document.querySelector('.photo-upload').addEventListener('click', function() {
            // 模拟照片选择
            this.innerHTML = `
                <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=120&h=120&fit=crop&crop=face" 
                     alt="已选择照片" class="w-full h-full object-cover rounded-lg">
            `;
        });
    </script>
</body>
</html> 