<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的牧场</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .status-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .status-active { background: #dcfce7; color: #166534; }
        .status-for-sale { background: #dbeafe; color: #1d4ed8; }
        .status-sold { background: #f3f4f6; color: #6b7280; }
        .status-dead { background: #fee2e2; color: #dc2626; }
        .livestock-card {
            transition: all 0.2s ease;
        }
        .livestock-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .add-button {
            position: relative;
            z-index: 10;
        }
        .add-button::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #10b981, #059669, #34d399);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .add-button:hover::before {
            opacity: 0.3;
        }
        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
            }
            50% {
                box-shadow: 0 0 0 8px rgba(16, 185, 129, 0);
            }
        }
        .add-button-icon {
            animation: pulse-glow 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="h-11 bg-white"></div>
    
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center">
                <i class="fas fa-cow text-green-600 text-xl mr-2"></i>
                <h1 class="text-lg font-semibold text-gray-900">我的牧场</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-search text-gray-600"></i>
                </button>
                <button class="p-2 rounded-full hover:bg-gray-100">
                    <i class="fas fa-filter text-gray-600"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="px-4 py-4 pb-20">
        <!-- 统计卡片 -->
        <div class="bg-white rounded-xl p-4 mb-4 shadow-sm">
            <div class="grid grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-green-600">24</div>
                    <div class="text-xs text-gray-500">存栏</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-blue-600">3</div>
                    <div class="text-xs text-gray-500">待售</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-gray-600">8</div>
                    <div class="text-xs text-gray-500">已售</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-red-600">1</div>
                    <div class="text-xs text-gray-500">死亡</div>
                </div>
            </div>
        </div>

        <!-- 牲畜列表 -->
        <div class="space-y-3">
            <!-- 牲畜卡片 1 -->
            <div class="livestock-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=80&h=80&fit=crop&crop=face" 
                         alt="牛001" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">牛001</h3>
                            <span class="status-badge status-active">存栏</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>物种: 牛 | 品种: 安格斯</div>
                            <div>性别: 公 | 年龄: 2岁3个月</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 牲畜卡片 2 -->
            <div class="livestock-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1548550023-2bdb3c5beed7?w=80&h=80&fit=crop&crop=face" 
                         alt="牛002" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">牛002</h3>
                            <span class="status-badge status-for-sale">待售</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>物种: 牛 | 品种: 黄牛</div>
                            <div>性别: 母 | 年龄: 1岁8个月</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 牲畜卡片 3 -->
            <div class="livestock-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1516467508483-a7212febe31a?w=80&h=80&fit=crop&crop=face" 
                         alt="羊001" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">羊001</h3>
                            <span class="status-badge status-active">存栏</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>物种: 羊 | 品种: 山羊</div>
                            <div>性别: 母 | 年龄: 3岁1个月</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 牲畜卡片 4 -->
            <div class="livestock-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1563281577-a7be47e20db9?w=80&h=80&fit=crop&crop=face" 
                         alt="猪001" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">猪001</h3>
                            <span class="status-badge status-sold">已售</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>物种: 猪 | 品种: 大白猪</div>
                            <div>性别: 阉割 | 年龄: 1岁2个月</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>

            <!-- 牲畜卡片 5 -->
            <div class="livestock-card bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                <div class="flex items-center space-x-3">
                    <img src="https://images.unsplash.com/photo-1560114928-40f1f1eb26a0?w=80&h=80&fit=crop&crop=face" 
                         alt="牛003" class="w-16 h-16 rounded-lg object-cover">
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <h3 class="font-semibold text-gray-900">牛003</h3>
                            <span class="status-badge status-active">存栏</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>物种: 牛 | 品种: 西门塔尔</div>
                            <div>性别: 公 | 年龄: 4岁6个月</div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex items-center justify-around">
            <!-- Tab 1: 牲畜 -->
            <button class="flex flex-col items-center py-2 px-3 rounded-lg bg-green-50 border border-green-200">
                <i class="fas fa-cow text-green-600 text-xl mb-1"></i>
                <span class="text-xs font-medium text-green-600">牲畜</span>
            </button>
            
            <!-- Tab 2: 添加 -->
            <button class="flex flex-col items-center py-1 px-3 rounded-lg hover:bg-gray-50 transition-all duration-200 add-button" onclick="goToAddPage()">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mb-1 shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-200 relative add-button-icon">
                    <i class="fas fa-plus text-white text-xl font-bold"></i>
                    <!-- 脉冲动画效果 -->
                    <div class="absolute inset-0 rounded-full bg-green-400 opacity-20 animate-ping"></div>
                </div>
                <span class="text-xs font-bold text-green-600">添加</span>
            </button>
            
            <!-- Tab 3: 我的 -->
            <button class="flex flex-col items-center py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors" onclick="goToProfile()">
                <i class="fas fa-user text-gray-400 text-xl mb-1"></i>
                <span class="text-xs font-medium text-gray-400">我的</span>
            </button>
        </div>
    </nav>

    <!-- 底部安全区域 -->
    <div class="h-20"></div>

    <script>
        // 导航功能
        function goToAddPage() {
            // 模拟跳转到添加牲畜页面
            alert('跳转到添加牲畜页面');
            // 实际开发中可以使用: window.location.href = 'add-livestock.html';
        }

        function goToProfile() {
            // 模拟跳转到个人中心页面
            alert('跳转到个人中心页面');
            // 实际开发中可以使用: window.location.href = 'profile.html';
        }

        // 搜索和筛选按钮功能
        document.querySelector('.fa-search').parentElement.addEventListener('click', function() {
            alert('跳转到搜索页面');
            // 实际开发中可以使用: window.location.href = 'search.html';
        });

        document.querySelector('.fa-filter').parentElement.addEventListener('click', function() {
            alert('打开筛选面板');
            // 实际开发中可以使用: window.location.href = 'filter.html';
        });

        // 牲畜卡片点击事件
        document.querySelectorAll('.livestock-card').forEach(card => {
            card.addEventListener('click', function() {
                const livestockName = this.querySelector('h3').textContent;
                alert(`查看${livestockName}详情`);
                // 实际开发中可以使用: window.location.href = 'livestock-detail.html?id=' + livestockId;
            });
        });
    </script>
</body>
</html> 