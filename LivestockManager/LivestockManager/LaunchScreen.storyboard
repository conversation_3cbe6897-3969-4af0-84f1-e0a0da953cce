<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23727" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23721"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="app-icon-container">
                                <rect key="frame" x="146.66666666666666" y="356" width="100" height="100"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="LaunchIcon" translatesAutoresizingMaskIntoConstraints="NO" id="app-icon-imageview">
                                        <rect key="frame" x="-24.999999999999986" y="-25" width="150" height="150"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="150" id="app-icon-height"/>
                                            <constraint firstAttribute="width" constant="150" id="app-icon-width"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="100" id="container-height"/>
                                    <constraint firstAttribute="width" constant="100" id="container-width"/>
                                    <constraint firstItem="app-icon-imageview" firstAttribute="centerX" secondItem="app-icon-container" secondAttribute="centerX" id="icon-center-x"/>
                                    <constraint firstItem="app-icon-imageview" firstAttribute="centerY" secondItem="app-icon-container" secondAttribute="centerY" id="icon-center-y"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" text="Professional Livestock Management Tool" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="subtitle-label">
                                <rect key="frame" x="40" y="526" width="313" height="19"/>
                                <fontDescription key="fontDescription" type="system" weight="light" pointSize="16"/>
                                <color key="textColor" red="0.33333333333333331" green="0.33333333333333331" blue="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" misplaced="YES" text="Livestock Manager" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="app-name-label">
                                <rect key="frame" x="40" y="489" width="313" height="29"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="24"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="app-icon-container" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="container-center-x"/>
                            <constraint firstItem="app-icon-container" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="-20" id="container-center-y"/>
                            <constraint firstItem="app-name-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="40" id="name-leading"/>
                            <constraint firstItem="app-name-label" firstAttribute="top" secondItem="app-icon-container" secondAttribute="bottom" constant="20" id="name-top-spacing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="app-name-label" secondAttribute="trailing" constant="40" id="name-trailing"/>
                            <constraint firstItem="subtitle-label" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="40" id="subtitle-leading"/>
                            <constraint firstItem="subtitle-label" firstAttribute="top" secondItem="app-name-label" secondAttribute="bottom" constant="10" id="subtitle-top-spacing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="subtitle-label" secondAttribute="trailing" constant="40" id="subtitle-trailing"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <image name="LaunchIcon" width="341.33334350585938" height="341.33334350585938"/>
    </resources>
</document>
