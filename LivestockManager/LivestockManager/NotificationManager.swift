import Foundation
import UserNotifications
import SwiftUI

class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isNotificationEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isNotificationEnabled, forKey: "isNotificationEnabled")
            if isNotificationEnabled {
                scheduleNotification()
            } else {
                cancelAllNotifications()
            }
        }
    }
    
    @Published var notificationTime: Date {
        didSet {
            UserDefaults.standard.set(notificationTime, forKey: "notificationTime")
            if isNotificationEnabled {
                scheduleNotification()
            }
        }
    }
    
    private let notificationCenter = UNUserNotificationCenter.current()
    
    private init() {
        // 读取保存的设置，如果不存在则使用默认值
        // 默认开启通知
        if UserDefaults.standard.object(forKey: "isNotificationEnabled") == nil {
            self.isNotificationEnabled = true
            UserDefaults.standard.set(true, forKey: "isNotificationEnabled")
        } else {
            self.isNotificationEnabled = UserDefaults.standard.bool(forKey: "isNotificationEnabled")
        }
        
        if let savedTime = UserDefaults.standard.object(forKey: "notificationTime") as? Date {
            self.notificationTime = savedTime
        } else {
            // 默认时间为16:01
            var components = DateComponents()
            components.hour = 16
            components.minute = 1
            
            let calendar = Calendar.current
            self.notificationTime = calendar.date(from: components) ?? Date()
            UserDefaults.standard.set(self.notificationTime, forKey: "notificationTime")
        }
        
        // 如果通知已启用，则检查权限并调度通知
        if self.isNotificationEnabled {
            checkAuthorizationStatus { isAuthorized in
                if isAuthorized {
                    self.scheduleNotification()
                }
            }
        }
    }
    
    // 请求通知权限
    func requestAuthorization(completion: @escaping (Bool) -> Void) {
        notificationCenter.requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    self.isNotificationEnabled = true
                    self.scheduleNotification()
                } else {
                    self.isNotificationEnabled = false
                }
                completion(granted)
            }
        }
    }
    
    // 检查通知权限状态
    func checkAuthorizationStatus(completion: @escaping (Bool) -> Void) {
        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                let isAuthorized = settings.authorizationStatus == .authorized
                completion(isAuthorized)
            }
        }
    }
    
    // 调度通知
    func scheduleNotification() {
        // 先移除现有通知
        cancelAllNotifications()
        
        // 获取选择的时间的小时和分钟
        let calendar = Calendar.current
        let components = calendar.dateComponents([.hour, .minute], from: notificationTime)
        
        // 创建通知内容
        let content = UNMutableNotificationContent()
        content.title = "养殖助手提醒"
        content.body = "是时候管理您的动物啦！点击查看详情。"
        content.sound = UNNotificationSound.default
        
        // 创建触发器，每天在指定时间触发
        var dateComponents = DateComponents()
        dateComponents.hour = components.hour
        dateComponents.minute = components.minute
        let trigger = UNCalendarNotificationTrigger(dateMatching: dateComponents, repeats: true)
        
        // 创建请求
        let request = UNNotificationRequest(
            identifier: "dailyReminder",
            content: content,
            trigger: trigger
        )
        
        // 添加通知请求
        notificationCenter.add(request) { error in
            if let error = error {
                print("通知调度失败: \(error.localizedDescription)")
            } else {
                print("通知成功调度在 \(components.hour ?? 0):\(components.minute ?? 0)")
            }
        }
    }
    
    // 取消所有通知
    func cancelAllNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    // 用于UIKit集成
    func openNotificationSettings() {
        if let url = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(url)
        }
    }
} 