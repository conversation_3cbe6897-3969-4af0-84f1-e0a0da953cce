import SwiftUI

struct FilterView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var dataManager: AnimalDataManager
    
    // 筛选条件状态 - 本地临时状态，应用前不会影响数据管理器
    @State private var selectedSpecies: Species? = nil
    @State private var selectedSex: Sex? = nil
    @State private var selectedStatus: AnimalStatus? = nil
    @State private var selectedAgeRange: AgeRange? = nil
    @State private var selectedSource: AnimalSource? = nil
    @State private var selectedQuickFilter: QuickFilter? = nil
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                Color(hex: "#F8F8F8")
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 物种筛选
                        FilterSection(title: L.Filter.species) {
                            FilterButtonGroup(
                                options: [nil] + Species.allCases,
                                selectedOption: $selectedSpecies,
                                allOptionTitle: L.Filter.all
                            ) { species in
                                Text(species?.displayName ?? L.Filter.all)
                            }
                        }
                        
                        // 性别筛选
                        FilterSection(title: L.Filter.gender) {
                            FilterButtonGroup(
                                options: [nil] + Sex.allCases,
                                selectedOption: $selectedSex,
                                allOptionTitle: L.Filter.all
                            ) { gender in
                                Text(gender?.displayName ?? L.Filter.all)
                            }
                        }
                        
                        // 状态筛选
                        FilterSection(title: L.Filter.status) {
                            VStack(spacing: 12) {
                                // 第一行
                                HStack(spacing: 12) {
                                    FilterButton(
                                        title: L.Filter.all,
                                        isSelected: selectedStatus == nil
                                    ) {
                                        selectedStatus = nil
                                    }
                                    
                                    FilterButton(
                                        title: L.Status.active,
                                        isSelected: selectedStatus == .active
                                    ) {
                                        selectedStatus = .active
                                    }
                                    
                                    FilterButton(
                                        title: L.Status.forSale,
                                        isSelected: selectedStatus == .forSale
                                    ) {
                                        selectedStatus = .forSale
                                    }
                                    
                                    FilterButton(
                                        title: L.Status.sold,
                                        isSelected: selectedStatus == .sold
                                    ) {
                                        selectedStatus = .sold
                                    }
                                }
                                
                                // 第二行
                                HStack {
                                    FilterButton(
                                        title: L.Status.deceased,
                                        isSelected: selectedStatus == .deceased
                                    ) {
                                        selectedStatus = .deceased
                                    }
                                    
                                    FilterButton(
                                        title: L.Status.other,
                                        isSelected: selectedStatus == .other
                                    ) {
                                        selectedStatus = .other
                                    }
                                    
                                    Spacer()
                                    Spacer()
                                }
                            }
                        }
                        
                        // 年龄范围筛选
                        FilterSection(title: L.Filter.age) {
                            FilterButtonGroup(
                                options: [nil] + AgeRange.allCases,
                                selectedOption: $selectedAgeRange,
                                allOptionTitle: L.Filter.all
                            ) { ageRange in
                                Text(ageRange?.displayName ?? L.Filter.all)
                            }
                        }
                        
                        // 来源筛选
                        FilterSection(title: L.Filter.source) {
                            HStack(spacing: 12) {
                                FilterButton(
                                    title: L.Filter.all,
                                    isSelected: selectedSource == nil
                                ) {
                                    selectedSource = nil
                                }
                                
                                FilterButton(
                                    title: L.Source.selfBred,
                                    isSelected: selectedSource == .selfBred
                                ) {
                                    selectedSource = .selfBred
                                }
                                
                                FilterButton(
                                    title: L.Source.purchased,
                                    isSelected: selectedSource == .purchased
                                ) {
                                    selectedSource = .purchased
                                }
                                
                                Spacer()
                            }
                        }
                        
                        // 快速筛选
                        FilterSection(title: L.Filter.quick) {
                            VStack(spacing: 12) {
                                QuickFilterCard(
                                    title: QuickFilter.healthy.displayName,
                                    subtitle: QuickFilter.healthy.description,
                                    iconName: "heart.fill",
                                    isSelected: selectedQuickFilter == .healthy
                                ) {
                                    selectedQuickFilter = selectedQuickFilter == .healthy ? nil : .healthy
                                    // 如果选中了快速筛选，清除相应的重复筛选条件
                                    if selectedQuickFilter == .healthy {
                                        selectedStatus = nil
                                    }
                                }
                                
                                QuickFilterCard(
                                    title: QuickFilter.forSale.displayName,
                                    subtitle: QuickFilter.forSale.description,
                                    iconName: "tag.fill",
                                    isSelected: selectedQuickFilter == .forSale
                                ) {
                                    selectedQuickFilter = selectedQuickFilter == .forSale ? nil : .forSale
                                    // 如果选中了快速筛选，清除相应的重复筛选条件
                                    if selectedQuickFilter == .forSale {
                                        selectedStatus = nil
                                    }
                                }
                                
                                QuickFilterCard(
                                    title: QuickFilter.young.displayName,
                                    subtitle: QuickFilter.young.description,
                                    iconName: "leaf.fill",
                                    isSelected: selectedQuickFilter == .young
                                ) {
                                    selectedQuickFilter = selectedQuickFilter == .young ? nil : .young
                                    // 如果选中了快速筛选，清除相应的重复筛选条件
                                    if selectedQuickFilter == .young {
                                        selectedAgeRange = nil
                                    }
                                }
                            }
                        }
                        
                        // 底部安全区域
                        Color.clear.frame(height: 100)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                }
                
                // 底部按钮
                VStack {
                    Spacer()
                    
                    HStack(spacing: 12) {
                        // 重置筛选按钮
                        Button(action: resetFilters) {
                            Text(L.Filter.reset)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.black)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(Color.white)
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.black.opacity(0.08), lineWidth: 1)
                                )
                        }
                        
                        // 应用筛选按钮
                        Button(action: applyFilters) {
                            Text(L.Filter.apply)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 16)
                                .background(Color.black)
                                .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 16)
                    .background(Color.white)
                    .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: -1)
                }
            }
            .navigationTitle(L.Filter.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(L.cancel) {
                        dismiss()
                    }
                    .foregroundColor(.black)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(L.Filter.reset) {
                        resetFilters()
                    }
                    .foregroundColor(.black)
                }
            }
            .onAppear {
                // 当视图出现时，加载当前筛选状态
                loadCurrentFilters()
            }
        }
    }
    
    // 加载当前筛选条件
    private func loadCurrentFilters() {
        selectedSpecies = dataManager.filterSpecies
        selectedSex = dataManager.filterSex
        selectedStatus = dataManager.filterStatus
        selectedAgeRange = dataManager.filterAgeRange
        selectedSource = dataManager.filterSource
        selectedQuickFilter = dataManager.filterQuickFilter
    }
    
    private func resetFilters() {
        selectedSpecies = nil
        selectedSex = nil
        selectedStatus = nil
        selectedAgeRange = nil
        selectedSource = nil
        selectedQuickFilter = nil
    }
    
    private func applyFilters() {
        // 将本地筛选条件应用到数据管理器
        dataManager.filterSpecies = selectedSpecies
        dataManager.filterSex = selectedSex
        dataManager.filterStatus = selectedStatus
        dataManager.filterAgeRange = selectedAgeRange
        dataManager.filterSource = selectedSource
        dataManager.filterQuickFilter = selectedQuickFilter
        
        // 应用筛选
        dataManager.applyFilters()
        
        // 关闭筛选页面
        dismiss()
    }
}

// MARK: - 筛选区块
struct FilterSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.black)
            
            content
        }
    }
}

// MARK: - 筛选按钮组
struct FilterButtonGroup<T: Hashable, Content: View>: View {
    let options: [T?]
    @Binding var selectedOption: T?
    let allOptionTitle: String
    let content: (T?) -> Content
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 4), spacing: 12) {
            ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                FilterButton(
                    title: getTitle(for: option),
                    isSelected: selectedOption == option
                ) {
                    selectedOption = option
                }
            }
        }
    }
    
    private func getTitle(for option: T?) -> String {
        if option == nil {
            return allOptionTitle
        }
        
        if let species = option as? Species {
            return species.displayName
        } else if let gender = option as? Sex {
            return gender.displayName
        } else if let status = option as? AnimalStatus {
            return status.displayName
        } else if let ageRange = option as? AgeRange {
            return ageRange.displayName
        } else if let source = option as? AnimalSource {
            return source.displayName
        }
        
        return allOptionTitle
    }
}

// MARK: - 筛选按钮
struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(isSelected ? .white : .black)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(isSelected ? Color.black : Color.white)
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.black.opacity(isSelected ? 0 : 0.08), lineWidth: 1)
                )
        }
    }
}

// MARK: - 快速筛选卡片
struct QuickFilterCard: View {
    let title: String
    let subtitle: String
    let iconName: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 14) {
                // 图标
                ZStack {
                    Circle()
                        .fill(Color.black.opacity(0.04))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 18))
                        .foregroundColor(.black)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.black)
                    
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.6))
                        .lineLimit(1)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(.black)
                        .font(.system(size: 14, weight: .semibold))
                        .padding(4)
                        .background(Color.black.opacity(0.08))
                        .clipShape(Circle())
                }
            }
            .padding(14)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.black : Color.black.opacity(0.08), lineWidth: isSelected ? 1.5 : 1)
            )
        }
    }
}

// MARK: - 年龄范围枚举
enum AgeRange: String, CaseIterable, Identifiable, Codable {
    case young = "young"
    case middle = "middle"
    case adult = "adult"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .young: return L.AgeRange.young
        case .middle: return L.AgeRange.middle
        case .adult: return L.AgeRange.adult
        }
    }
}

// MARK: - 快速筛选枚举
enum QuickFilter: String, CaseIterable, Identifiable, Codable {
    case healthy = "healthy"
    case forSale = "forSale"
    case young = "young"
    
    var id: String { self.rawValue }
    
    var displayName: String {
        switch self {
        case .healthy: return L.Filter.quickHealthy
        case .forSale: return L.Filter.quickForSale
        case .young: return L.Filter.quickYoung
        }
    }
    
    var description: String {
        switch self {
        case .healthy: return L.Filter.quickHealthyDesc
        case .forSale: return L.Filter.quickForSaleDesc
        case .young: return L.Filter.quickYoungDesc
        }
    }
}

#Preview {
    FilterView()
} 