import Foundation
import SwiftUI

class AnimalDataManager: ObservableObject {
    @Published var livestock: [Animal] = []
    @Published var filteredLivestock: [Animal] = []
    
    // 筛选条件
    @Published var filterSpecies: Species? = nil
    @Published var filterSex: Sex? = nil
    @Published var filterStatus: AnimalStatus? = nil
    @Published var filterAgeRange: AgeRange? = nil
    @Published var filterSource: AnimalSource? = nil
    @Published var filterQuickFilter: QuickFilter? = nil
    
    private var imagesCache: [String: UIImage] = [:]
    
    // 文件URL
    private var fileURL: URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsDirectory.appendingPathComponent("livestock_data.json")
    }
    
    // 图片目录URL
    private var imagesDirectoryURL: URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsDirectory.appendingPathComponent("images", isDirectory: true)
    }
    
    init() {
        // 确保图片目录存在
        createImagesDirectoryIfNeeded()
        
        // 检查文件系统权限
        checkFileSystemPermissions()
        
        // 尝试加载保存的数据，如果没有则初始化为空数组
        if !loadSavedData() {
            // 初始化为空数组
            livestock = []
            // 保存空数组作为初始数据
            saveLivestockData()
        }
        
        // 初始化筛选后的列表
        filteredLivestock = livestock
        
        // 注册应用程序生命周期通知
        registerAppLifecycleObservers()
    }
    
    // 析构函数，确保退出时保存数据
    deinit {
        print("AnimalDataManager 被释放，确保保存数据...")
        saveLivestockData()
        
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
    }
    
    // 创建图片目录（如果不存在）
    private func createImagesDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: imagesDirectoryURL.path) {
            do {
                try FileManager.default.createDirectory(at: imagesDirectoryURL, withIntermediateDirectories: true)
                print("成功创建图片目录")
            } catch {
                print("创建图片目录失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 检查文件系统权限
    private func checkFileSystemPermissions() {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let testFilePath = documentsDirectory.appendingPathComponent("write_permission_test.txt")
        
        // 尝试写入测试文件
        do {
            try "test".write(to: testFilePath, atomically: true, encoding: .utf8)
            print("文件系统写入权限检查通过")
            
            // 如果成功写入，删除测试文件
            try FileManager.default.removeItem(at: testFilePath)
        } catch {
            print("文件系统写入权限检查失败: \(error.localizedDescription)")
        }
    }
    
    // 保存图片到文件系统
    func saveImage(_ image: UIImage) -> String? {
        // 检查图片尺寸是否有效
        print("📷 保存图片 - 尺寸: \(image.size.width) x \(image.size.height)")
        
        if image.size.width <= 0 || image.size.height <= 0 || 
           !image.size.width.isFinite || !image.size.height.isFinite {
            print("❌ 图片尺寸无效: \(image.size)")
            return nil
        }
        
        // 生成唯一文件名
        let fileName = UUID().uuidString + ".jpg"
        let fileURL = imagesDirectoryURL.appendingPathComponent(fileName)
        
        // 将图片转换为JPEG数据
        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            print("❌ 图片转换为JPEG失败")
            return nil
        }
        
        // 写入文件
        do {
            try imageData.write(to: fileURL)
            print("✅ 图片保存成功: \(fileName)")
            
            // 添加到缓存
            imagesCache[fileName] = image
            
            return fileName
        } catch {
            print("❌ 图片保存失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    // 从文件系统加载图片
    func loadImage(fileName: String) -> UIImage? {
        // 先检查缓存
        if let cachedImage = imagesCache[fileName] {
            return cachedImage
        }
        
        // 构建文件URL
        let fileURL = imagesDirectoryURL.appendingPathComponent(fileName)
        
        // 尝试加载图片
        if let imageData = try? Data(contentsOf: fileURL),
           let image = UIImage(data: imageData) {
            // 添加到缓存
            imagesCache[fileName] = image
            return image
        }
        
        print("无法加载图片: \(fileName)")
        return nil
    }
    
    // 加载保存的数据
    private func loadSavedData() -> Bool {
        print("========== 开始加载本地数据 ==========")
        print("尝试从以下路径加载数据: \(fileURL.path)")
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            print("❌ 没有找到保存的数据文件")
            print("========== 加载本地数据结束 ==========")
            return false
        }
        
        do {
            print("📂 找到数据文件，开始读取...")
            let data = try Data(contentsOf: fileURL)
            print("✅ 成功读取数据文件，大小: \(data.count) 字节")
            
            let decoder = JSONDecoder()
            
            // 设置日期解码策略
            decoder.dateDecodingStrategy = .iso8601
            
            print("🔄 开始解析JSON数据...")
            self.livestock = try decoder.decode([Animal].self, from: data)
            print("✅ 成功加载了\(self.livestock.count)个牲畜记录")
            
            // 打印加载的数据简要信息
            if self.livestock.isEmpty {
                print("⚠️ 数据文件存在但没有记录")
            } else {
                print("📋 加载的记录摘要:")
                for (index, animal) in self.livestock.prefix(5).enumerated() {
                    print("  [\(index)] ID: \(animal.id.uuidString.prefix(8))..., 标识: \(animal.visualId), 物种: \(animal.species.rawValue)")
                }
                
                if self.livestock.count > 5 {
                    print("  ... 以及其他\(self.livestock.count - 5)条记录")
                }
            }
            
            print("========== 加载本地数据完成 ==========")
            return true
        } catch {
            print("❌ 加载数据时发生错误: \(error.localizedDescription)")
            // 如果是解码错误，尝试打印更多信息
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .dataCorrupted(let context):
                    print("  数据损坏: \(context.debugDescription)")
                case .keyNotFound(let key, let context):
                    print("  未找到键: \(key.stringValue), 路径: \(context.codingPath)")
                case .typeMismatch(let type, let context):
                    print("  类型不匹配: 期望 \(type), 路径: \(context.codingPath)")
                case .valueNotFound(let type, let context):
                    print("  未找到值: 期望 \(type), 路径: \(context.codingPath)")
                @unknown default:
                    print("  未知解码错误")
                }
            }
            
            print("========== 加载本地数据失败 ==========")
            return false
        }
    }
    
    // 保存牲畜数据
    private func saveLivestockData() {
        print("========== 开始保存数据 ==========")
        print("尝试保存数据到: \(fileURL.path)")
        print("待保存的牲畜数量: \(livestock.count)")
        
        // 确保父目录存在
        let directoryURL = fileURL.deletingLastPathComponent()
        if !FileManager.default.fileExists(atPath: directoryURL.path) {
            do {
                try FileManager.default.createDirectory(at: directoryURL, withIntermediateDirectories: true)
                print("✅ 创建父目录成功: \(directoryURL.path)")
            } catch {
                print("❌ 创建父目录失败: \(error.localizedDescription)")
                print("========== 保存数据失败 ==========")
                return
            }
        }
        
        do {
            print("🔄 开始编码数据...")
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            
            // 设置日期编码策略
            encoder.dateEncodingStrategy = .iso8601
            
            let data = try encoder.encode(livestock)
            print("✅ 编码成功，数据大小: \(data.count) 字节")
            
            print("💾 正在写入文件...")
            try data.write(to: fileURL, options: .atomic)
            
            // 验证文件是否已保存
            if FileManager.default.fileExists(atPath: fileURL.path) {
                print("✅ 成功保存了\(self.livestock.count)个牲畜记录到文件")
                
                // 简要打印保存的数据
                if self.livestock.isEmpty {
                    print("⚠️ 保存了空数据集")
                } else {
                    print("📋 保存的记录摘要:")
                    for (index, animal) in self.livestock.prefix(5).enumerated() {
                        print("  [\(index)] 保存: ID: \(animal.id.uuidString.prefix(8))..., 标识: \(animal.visualId), 物种: \(animal.species.rawValue)")
                    }
                    
                    if self.livestock.count > 5 {
                        print("  ... 以及其他\(self.livestock.count - 5)条记录")
                    }
                }
            } else {
                print("⚠️ 警告：保存后文件未找到！")
            }
            
            print("========== 保存数据完成 ==========")
        } catch {
            print("❌ 保存数据时发生错误: \(error.localizedDescription)")
            print("========== 保存数据失败 ==========")
        }
    }
    
    // 获取统计数据 - 根据筛选后的数据计算
    var statistics: AnimalStatistics {
        let inStock = filteredLivestock.filter { $0.status == .active }.count
        let forSale = filteredLivestock.filter { $0.status == .forSale }.count
        let sold = filteredLivestock.filter { $0.status == .sold }.count
        let dead = filteredLivestock.filter { $0.status == .deceased }.count
        
        return AnimalStatistics(
            inStock: inStock,
            forSale: forSale,
            sold: sold,
            dead: dead
        )
    }
    
    // 添加牲畜
    func addAnimal(_ livestock: Animal) {
        self.livestock.append(livestock)
        applyFilters() // 重新应用筛选
        saveLivestockData() // 保存更改
    }
    
    // 删除牲畜
    func deleteAnimal(_ livestock: Animal) {
        self.livestock.removeAll { $0.id == livestock.id }
        applyFilters() // 重新应用筛选
        saveLivestockData() // 保存更改
    }
    
    // 更新牲畜
    func updateAnimal(_ livestock: Animal) {
        if let index = self.livestock.firstIndex(where: { $0.id == livestock.id }) {
            self.livestock[index] = livestock
            applyFilters() // 重新应用筛选
            saveLivestockData() // 保存更改
        }
    }
    
    // 应用筛选器
    func applyFilters() {
        // 开始筛选前，从所有牲畜开始
        filteredLivestock = livestock
        
        // 应用物种筛选
        if let species = filterSpecies {
            filteredLivestock = filteredLivestock.filter { $0.species == species }
        }
        
        // 应用性别筛选
        if let sex = filterSex {
            filteredLivestock = filteredLivestock.filter { $0.sex == sex }
        }
        
        // 应用状态筛选
        if let status = filterStatus {
            filteredLivestock = filteredLivestock.filter { $0.status == status }
        }
        
        // 应用来源筛选
        if let source = filterSource {
            filteredLivestock = filteredLivestock.filter { $0.source == source }
        }
        
        // 应用年龄筛选
        if let ageRange = filterAgeRange {
            filteredLivestock = filteredLivestock.filter { animal in
                guard let birthDate = animal.birthDate else { return false }
                
                let calendar = Calendar.current
                let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
                let years = ageComponents.year ?? 0
                
                switch ageRange {
                case .young: // 0-1岁
                    return years < 1
                case .middle: // 1-3岁
                    return years >= 1 && years <= 3
                case .adult: // 3岁以上
                    return years > 3
                }
            }
        }
        
        // 应用快速筛选
        if let quickFilter = filterQuickFilter {
            switch quickFilter {
            case .healthy:
                filteredLivestock = filteredLivestock.filter { $0.status == .active }
            case .forSale:
                filteredLivestock = filteredLivestock.filter { $0.status == .forSale }
            case .young:
                filteredLivestock = filteredLivestock.filter { animal in
                    guard let birthDate = animal.birthDate else { return false }
                    
                    let calendar = Calendar.current
                    let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
                    let years = ageComponents.year ?? 0
                    
                    return years < 1
                }
            }
        }
    }
    
    // 重置所有筛选器
    func resetFilters() {
        filterSpecies = nil
        filterSex = nil
        filterStatus = nil
        filterAgeRange = nil
        filterSource = nil
        filterQuickFilter = nil
        
        // 重置后重新应用（即显示所有牲畜）
        filteredLivestock = livestock
    }
    
    // 检查是否有活跃的筛选器
    var hasActiveFilters: Bool {
        return filterSpecies != nil || 
               filterSex != nil || 
               filterStatus != nil || 
               filterAgeRange != nil || 
               filterSource != nil || 
               filterQuickFilter != nil
    }
    
    /// 替换所有动物数据，用于数据迁移
    func replaceAllAnimals(with animals: [Animal]) {
        self.livestock = animals
        saveLivestockData()
        applyFilters() // 更新筛选结果
    }
    
    // MARK: - 应用程序生命周期处理
    
    /// 注册应用程序生命周期通知
    private func registerAppLifecycleObservers() {
        // 注册应用进入后台通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        
        // 注册应用即将终止通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillTerminate),
            name: UIApplication.willTerminateNotification,
            object: nil
        )
        
        // 注册应用即将失去焦点通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
        
        print("已注册应用程序生命周期通知")
    }
    
    /// 应用进入后台时保存数据
    @objc private func appDidEnterBackground() {
        print("📱 应用进入后台，正在保存数据...")
        saveLivestockData()
    }
    
    /// 应用即将终止时保存数据
    @objc private func appWillTerminate() {
        print("📱 应用即将终止，正在保存数据...")
        saveLivestockData()
    }
    
    /// 应用即将失去焦点时保存数据
    @objc private func appWillResignActive() {
        print("📱 应用即将失去焦点，正在保存数据...")
        saveLivestockData()
    }
    
    // MARK: - 调试辅助方法
    
    /// 强制重新加载数据
    func forceReloadData() {
        print("========== 开始强制重新加载数据 ==========")
        print("🔄 开始重新加载数据...")
        if loadSavedData() {
            print("✅ 成功重新加载数据，加载了 \(livestock.count) 个记录")
            // 更新筛选后的列表
            print("🔍 重新应用筛选条件...")
            applyFilters()
            print("✅ 筛选完成，筛选后记录数: \(filteredLivestock.count)")
        } else {
            print("❌ 重新加载数据失败")
        }
        print("========== 强制重新加载数据完成 ==========")
    }
    
    /// 清除所有保存的数据（仅用于调试）
    func clearAllData() {
        print("正在清除所有数据...")
        
        // 清除内存中的数据
        livestock = []
        filteredLivestock = []
        
        // 清除文件系统中的数据
        do {
            if FileManager.default.fileExists(atPath: fileURL.path) {
                try FileManager.default.removeItem(at: fileURL)
                print("成功删除数据文件")
            }
            
            // 清除图片缓存
            imagesCache = [:]
            
            // 清除图片目录
            if FileManager.default.fileExists(atPath: imagesDirectoryURL.path) {
                try FileManager.default.removeItem(at: imagesDirectoryURL)
                print("成功删除图片目录")
                
                // 重新创建空的图片目录
                createImagesDirectoryIfNeeded()
            }
            
            print("所有数据已清除")
        } catch {
            print("清除数据失败: \(error.localizedDescription)")
        }
    }
} 