import Foundation
import SwiftUI

class UserSettingsManager: ObservableObject {
    static let shared = UserSettingsManager()
    
    @Published var farmerName: String {
        didSet {
            UserDefaults.standard.set(farmerName, forKey: "farmerName")
        }
    }
    
    @Published var firstLaunchDate: Date {
        didSet {
            UserDefaults.standard.set(firstLaunchDate, forKey: "firstLaunchDate")
        }
    }
    
    private init() {
        // 从UserDefaults加载牧场主名称，如果没有则使用默认值
        self.farmerName = UserDefaults.standard.string(forKey: "farmerName") ?? L.Profile.userTitle
        
        // 从UserDefaults加载首次启动日期，如果没有则使用当前日期
        if let savedDate = UserDefaults.standard.object(forKey: "firstLaunchDate") as? Date {
            self.firstLaunchDate = savedDate
        } else {
            self.firstLaunchDate = Date()
            UserDefaults.standard.set(firstLaunchDate, forKey: "firstLaunchDate")
        }
    }
    
    // 计算使用天数
    var daysUsed: Int {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: firstLaunchDate, to: Date())
        return components.day ?? 0
    }
} 