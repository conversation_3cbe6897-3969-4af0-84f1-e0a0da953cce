import SwiftUI

// MARK: - String Extension for Localization
extension String {
    var localized: String {
        NSLocalizedString(self, comment: "")
    }
    
    func localized(with arguments: CVarArg...) -> String {
        String(format: self.localized, arguments: arguments)
    }
}

// MARK: - Localization Convenience Functions
enum L {
    // General
    static let appName = "app.name".localized
    static let cancel = "cancel".localized
    static let save = "save".localized
    static let update = "update".localized
    static let edit = "edit".localized
    static let delete = "delete".localized
    static let confirm = "confirm".localized
    static let done = "done".localized
    static let unknown = "unknown".localized
    static let required = "required".localized
    static let retry = "actions.retry".localized
    static let refresh = "actions.refresh".localized
    
    // Tabs
    static let tabHome = "tab.home".localized
    static let tabAdd = "tab.add".localized
    static let tabProfile = "tab.profile".localized
    static let tabTodo = "tab.todo".localized
    static let tabSearch = "tab.search".localized
    static let tabTools = "tab.tools".localized
    static let navMyFarm = "nav.myFarm".localized
    
    // Todo
    enum Todo {
        static let title = "todo.title".localized
        static let addPlaceholder = "todo.add.placeholder".localized
        static let hideCompleted = "todo.hide.completed".localized
        static let showCompleted = "todo.show.completed".localized
        static let empty = "todo.empty".localized
        static let emptyDesc = "todo.empty.desc".localized
        static let allCompleted = "todo.all.completed".localized
        static let viewCompleted = "todo.view.completed".localized
        static let exampleTask1 = "todo.example.task1".localized
        static let exampleTask2 = "todo.example.task2".localized
        static let exampleTask3 = "todo.example.task3".localized
    }
    
    // Home
    enum Home {
        static let emptyWelcome = "home.empty.welcome".localized
        static let emptyNoAnimals = "home.empty.noAnimals".localized
        static let emptyStartAdding = "home.empty.startAdding".localized
        static let emptyAddFirst = "home.empty.addFirst".localized
        static let featuresTitle = "home.features.title".localized
        static let featuresRecords = "home.features.records".localized
        static let featuresRecordsDesc = "home.features.records.desc".localized
        static let featuresSearch = "home.features.search".localized
        static let featuresSearchDesc = "home.features.search.desc".localized
        static let featuresFilter = "home.features.filter".localized
        static let featuresFilterDesc = "home.features.filter.desc".localized
        static let featuresStats = "home.features.stats".localized
        static let featuresStatsDesc = "home.features.stats.desc".localized
        static let tipsTitle = "home.tips.title".localized
        static let tipsPhotos = "home.tips.photos".localized
        static let tipsIds = "home.tips.ids".localized
        static let tipsUpdates = "home.tips.updates".localized
        static let tipsFeatures = "home.tips.features".localized
    }
    
    // Filter
    enum Filter {
        static let title = "filter.title".localized
        static let applied = "filter.applied".localized
        static let clear = "filter.clear".localized
        static let clearAll = "filter.clearAll".localized
        static let apply = "filter.apply".localized
        static let reset = "filter.reset".localized
        static let noResults = "filter.noResults".localized
        static let noResultsHelp = "filter.noResults.help".localized
        static let all = "filter.all".localized
        static let species = "filter.species".localized
        static let gender = "filter.gender".localized
        static let status = "filter.status".localized
        static let age = "filter.age".localized
        static let source = "filter.source".localized
        static let quick = "filter.quick".localized
        static let quickHealthy = "filter.quick.healthy".localized
        static let quickHealthyDesc = "filter.quick.healthy.desc".localized
        static let quickForSale = "filter.quick.forSale".localized
        static let quickForSaleDesc = "filter.quick.forSale.desc".localized
        static let quickYoung = "filter.quick.young".localized
        static let quickYoungDesc = "filter.quick.young.desc".localized
    }
    
    // Animal Properties
    enum Animal {
        static let id = "animal.id".localized
        static let species = "animal.species".localized
        static let breed = "animal.breed".localized
        static let gender = "animal.gender".localized
        static let birthDate = "animal.birthDate".localized
        static let age = "animal.age".localized
        static let source = "animal.source".localized
        static let purchaseDate = "animal.purchaseDate".localized
        static let purchasePrice = "animal.purchasePrice".localized
        static let status = "animal.status".localized
        static let notes = "animal.notes".localized
        static let photo = "animal.photo".localized
        static let addPhoto = "animal.addPhoto".localized
    }
    
    // Species
    enum Species {
        static let cattle = "species.cattle".localized
        static let sheep = "species.sheep".localized
        static let pig = "species.pig".localized
        static let horse = "species.horse".localized
        static let chicken = "species.chicken".localized
        static let duck = "species.duck".localized
        static let goose = "species.goose".localized
        static let other = "species.other".localized
    }
    
    // Gender
    enum Gender {
        static let male = "gender.male".localized
        static let female = "gender.female".localized
        static let castrated = "gender.castrated".localized
    }
    
    // Source
    enum Source {
        static let selfBred = "source.selfBred".localized
        static let purchased = "source.purchased".localized
    }
    
    // Status
    enum Status {
        static let active = "status.active".localized
        static let forSale = "status.forSale".localized
        static let sold = "status.sold".localized
        static let deceased = "status.deceased".localized
        static let other = "status.other".localized
    }
    
    // Age Range
    enum AgeRange {
        static let young = "ageRange.young".localized
        static let middle = "ageRange.middle".localized
        static let adult = "ageRange.adult".localized
    }
    
    // Add/Edit
    enum Add {
        static let title = "add.title".localized
        static let editTitle = "edit.title".localized
        static let idHint = "add.id.hint".localized
        static let breedPlaceholder = "add.breed.placeholder".localized
        static let pricePlaceholder = "add.price.placeholder".localized
        static let notesPlaceholder = "add.notes.placeholder".localized
        static let requiredFields = "add.requiredFields".localized
    }
    
    // Detail
    enum Detail {
        static let basicInfo = "detail.basicInfo".localized
        static let statusInfo = "detail.statusInfo".localized
        static let notes = "detail.notes".localized
        static let deleteConfirm = "detail.deleteConfirm".localized
        static let deleteMessage = "detail.deleteMessage".localized
    }
    
    // Statistics
    enum Stats {
        static let inStock = "stats.inStock".localized
        static let forSale = "stats.forSale".localized
        static let sold = "stats.sold".localized
        static let deceased = "stats.deceased".localized
    }
    
    // Search
    enum Search {
        static let placeholder = "search.placeholder".localized
        static let noResults = "search.noResults".localized
        static let recentSearches = "search.recentSearches".localized
        static let clearHistory = "search.clearHistory".localized
        static let hotSearches = "search.hotSearches".localized
        static let quickSearch = "search.quickSearch".localized
    }
    
    // Tools
    enum Tools {
        static let title = "tools.title".localized
        static let counter = "tools.counter".localized
        static let calculator = "tools.calculator".localized
        static let weather = "tools.weather".localized
        static let marketPrice = "tools.marketPrice".localized
        static let timer = "tools.timer".localized
        static let notes = "tools.notes".localized
        static let calendar = "tools.calendar".localized
        static let converter = "tools.converter".localized
        static let medicine = "tools.medicine".localized
        static let basicTools = "tools.basicTools".localized
        static let suggestions = "tools.suggestions".localized
        static let suggestNewTool = "tools.suggestNewTool".localized
        
        // Weather (New - integrating keys from WeatherView and Localizable.strings)
        enum Weather {
            static let title = "tools.weather".localized // Already exists as L.Tools.weather
            static let loading = "tools.weather.loading".localized
            static let errorLocation = "tools.weather.errorLocation".localized // For permission denied
            static let errorUnknown = "tools.weather.errorUnknown".localized // For unknown errors and permission unknown
            static let errorFetch = "tools.weather.errorFetch".localized // For general fetch errors (New key)
            static let errorLocationUnavailable = "tools.weather.errorLocationUnavailable".localized // When location cannot be fetched (New key)
            static let errorManagerFailed = "tools.weather.errorManagerFailed".localized // When CLLocationManager fails (New key)
            static let locationPermissionTitle = "tools.weather.locationPermissionTitle".localized // 新增
            static let needsPermissionMessage = "tools.weather.needsPermissionMessage".localized // 新增
            static let farmingSuggestionTitle = "tools.weather.farmingSuggestionTitle".localized // 新增

            static let refresh = "tools.weather.refresh".localized
            static let hourlyForecast = "tools.weather.hourlyForecast".localized
            static let dailyForecast = "tools.weather.dailyForecast".localized
            static let farmingSuggestion = "tools.weather.farmingSuggestions".localized // farmingSuggestions vs farmingSuggestion
            static let goToSettings = "tools.weather.goToSettings".localized
            static let needsPermission = "tools.weather.needsPermission".localized
            static let locationPermission = "tools.weather.locationPermission".localized
            static let cancel = "cancel".localized // General cancel, already L.cancel
            static let feelsLike = "tools.weather.feelsLike".localized
            static let humidity = "tools.weather.humidity".localized
            static let wind = "tools.weather.wind".localized
            static let uvIndex = "tools.weather.uvIndex".localized
            static let visibility = "tools.weather.visibility".localized
            static let precipitation = "tools.weather.precipitation".localized
            static let today = "tools.weather.today".localized
            static let tomorrow = "tools.weather.tomorrow".localized
            static let noData = "tools.weather.noData".localized
            static let gettingLocation = "tools.weather.gettingLocation".localized // (New key)
            static let unknownLocation = "tools.weather.unknownLocation".localized // (New key)
            static let noSpecificSuggestionMessage = "tools.weather.noSpecificSuggestionMessage".localized

            enum UV {
                static let low = "tools.weather.uv.low".localized // (New key structure)
                static let moderate = "tools.weather.uv.moderate".localized // (New key structure)
                static let high = "tools.weather.uv.high".localized // (New key structure)
                static let veryHigh = "tools.weather.uv.veryHigh".localized // (New key structure)
                static let extreme = "tools.weather.uv.extreme".localized // (New key structure)
            }

            enum Suggestion {
                static let uvHigh = "tools.weather.suggestion.uvHigh".localized // (New key structure)
                static let heavyRainToday = "tools.weather.suggestion.heavyRainToday".localized // (New key structure)
                static let strongWindToday = "tools.weather.suggestion.strongWindToday".localized // (New key structure)
                static let rainNext3Days = "tools.weather.suggestion.rainNext3Days".localized // (New key structure)
                static let heatWave = "tools.weather.suggestion.heatWave".localized // (New key structure)
                static let coldSpell = "tools.weather.suggestion.coldSpell".localized // (New key structure)
                static let irrigation = "tools.weather.suggestion.irrigation".localized // (New key structure)
            }
        }
        
        // 计数器工具
        enum Counter {
            static let start = "tools.counter.start".localized
            static let reset = "tools.counter.reset".localized
            static let complete = "tools.counter.complete".localized
            static let counting = "tools.counter.counting".localized
            static let startTime = "tools.counter.startTime".localized
            static let newCount = "tools.counter.new".localized
            
            // 记录 (NEW)
            enum Record {
                static let title = "tools.counter.record.title".localized
                static let details = "tools.counter.record.details".localized
                static let count = "tools.counter.record.count".localized
                static let namePlaceholder = "tools.counter.record.namePlaceholder".localized
                static let notePlaceholder = "tools.counter.record.notePlaceholder".localized
            }
            
            // 历史 (NEW)
            enum History {
                static let title = "tools.counter.history.title".localized
                static let empty = "tools.counter.history.empty".localized
                static let countLabel = "tools.counter.history.countLabel".localized // e.g., "数量: %lld"
                static let timeLabel = "tools.counter.history.timeLabel".localized   // e.g., "时间: %@ - %@"
                static let noteLabel = "tools.counter.history.noteLabel".localized   // e.g., "备注: %@"
            }
        }
        
        // 单位转换工具
        enum Converter {
            static let value = "tools.converter.value".localized
            static let from = "tools.converter.from".localized
            static let to = "tools.converter.to".localized
            static let result = "tools.converter.result".localized
            static let reference = "tools.converter.reference".localized
            static let placeholder = "tools.converter.placeholder".localized
            static let selectUnit = "tools.converter.selectUnit".localized

            // 单位类别
            static let area = "tools.converter.area".localized
            static let weight = "tools.converter.weight".localized
            static let volume = "tools.converter.volume".localized
            static let length = "tools.converter.length".localized
            static let temperature = "tools.converter.temperature".localized

            // 单位名称
            enum Unit {
                // 面积单位
                static let squareMeter = "tools.converter.unit.squareMeter".localized
                static let mu = "tools.converter.unit.mu".localized
                static let hectare = "tools.converter.unit.hectare".localized
                static let squareKilometer = "tools.converter.unit.squareKilometer".localized
                static let acre = "tools.converter.unit.acre".localized

                // 重量单位
                static let kilogram = "tools.converter.unit.kilogram".localized
                static let gram = "tools.converter.unit.gram".localized
                static let jin = "tools.converter.unit.jin".localized
                static let ton = "tools.converter.unit.ton".localized
                static let pound = "tools.converter.unit.pound".localized

                // 容量单位
                static let liter = "tools.converter.unit.liter".localized
                static let milliliter = "tools.converter.unit.milliliter".localized
                static let cubicMeter = "tools.converter.unit.cubicMeter".localized
                static let gallon = "tools.converter.unit.gallon".localized

                // 长度单位
                static let meter = "tools.converter.unit.meter".localized
                static let centimeter = "tools.converter.unit.centimeter".localized
                static let kilometer = "tools.converter.unit.kilometer".localized
                static let foot = "tools.converter.unit.foot".localized
                static let inch = "tools.converter.unit.inch".localized

                // 温度单位
                static let celsius = "tools.converter.unit.celsius".localized
                static let fahrenheit = "tools.converter.unit.fahrenheit".localized
                static let kelvin = "tools.converter.unit.kelvin".localized
            }

            // 常用换算参考
            enum Reference {
                static let muToSqM = "tools.converter.reference.muToSqM".localized
                static let hectareToSqM = "tools.converter.reference.hectareToSqM".localized
                static let acreToSqM = "tools.converter.reference.acreToSqM".localized
                static let kgToJin = "tools.converter.reference.kgToJin".localized
                static let tonToKg = "tools.converter.reference.tonToKg".localized
                static let poundToKg = "tools.converter.reference.poundToKg".localized
                static let literToMl = "tools.converter.reference.literToMl".localized
                static let gallonToLiter = "tools.converter.reference.gallonToLiter".localized
                static let kmToM = "tools.converter.reference.kmToM".localized
                static let footToM = "tools.converter.reference.footToM".localized
                static let inchToCm = "tools.converter.reference.inchToCm".localized
                static let celsiusToFahrenheit = "tools.converter.reference.celsiusToFahrenheit".localized
                static let fahrenheitToCelsius = "tools.converter.reference.fahrenheitToCelsius".localized
                static let celsiusToKelvin = "tools.converter.reference.celsiusToKelvin".localized
            }
        }

        // 生产跟踪器
        enum ProductionTracker {
            static let title = "tools.calculator.production.title".localized
            static let description = "tools.calculator.production.description".localized
            static let animalType = "tools.calculator.production.animalType".localized
            static let animalCount = "tools.calculator.production.animalCount".localized
            static let productionAmount = "tools.calculator.production.productionAmount".localized
            static let date = "tools.calculator.production.date".localized
            static let addRecord = "tools.calculator.production.addRecord".localized
            static let records = "tools.calculator.production.records".localized
            static let averagePerAnimal = "tools.calculator.production.averagePerAnimal".localized
            static let totalProduction = "tools.calculator.production.totalProduction".localized
            static let expectedRange = "tools.calculator.production.expectedRange".localized
            static let noRecords = "tools.calculator.production.noRecords".localized
            static let recentRecords = "tools.calculator.production.recentRecords".localized
            static let productionStats = "tools.calculator.production.stats".localized
            static let productionTrend = "tools.calculator.production.trend".localized
            static let productionAdvice = "tools.calculator.production.advice".localized
            static let statisticalPeriod = "tools.calculator.production.statisticalPeriod".localized
            static let days = "tools.calculator.production.days".localized
            static let perDay = "tools.calculator.production.perDay".localized
            static let compareWithExpected = "tools.calculator.production.compareWithExpected".localized
            static let expected = "tools.calculator.production.expected".localized
            static let inputTotalProduction = "tools.calculator.production.inputTotalProduction".localized
            static let production = "tools.calculator.production.production".localized

            // 动物类型
            static let cow = "tools.calculator.production.cow".localized
            static let goat = "tools.calculator.production.goat".localized
            static let sheep = "tools.calculator.production.sheep".localized
            static let chicken = "tools.calculator.production.chicken".localized

            // 产品类型
            static let milk = "tools.calculator.production.milk".localized
            static let egg = "tools.calculator.production.egg".localized

            // 单位
            static let liter = "tools.calculator.production.liter".localized
            static let piece = "tools.calculator.production.piece".localized

            // 趋势
            static let trendRising = "tools.calculator.production.trend.rising".localized
            static let trendFalling = "tools.calculator.production.trend.falling".localized
            static let trendStable = "tools.calculator.production.trend.stable".localized
            static let trendNoData = "tools.calculator.production.trend.noData".localized
            static let trendInsufficientData = "tools.calculator.production.trend.insufficientData".localized

            // 时间段
            static let days7 = "tools.calculator.production.period.7days".localized
            static let days14 = "tools.calculator.production.period.14days".localized
            static let days30 = "tools.calculator.production.period.30days".localized
            static let days90 = "tools.calculator.production.period.90days".localized

            // 生产建议
            enum Advice {
                enum Cow {
                    static let low = "tools.calculator.production.advice.cow.low".localized
                    static let high = "tools.calculator.production.advice.cow.high".localized
                    static let normal = "tools.calculator.production.advice.cow.normal".localized
                }

                enum Goat {
                    static let low = "tools.calculator.production.advice.goat.low".localized
                    static let high = "tools.calculator.production.advice.goat.high".localized
                    static let normal = "tools.calculator.production.advice.goat.normal".localized
                }

                enum Sheep {
                    static let low = "tools.calculator.production.advice.sheep.low".localized
                    static let high = "tools.calculator.production.advice.sheep.high".localized
                    static let normal = "tools.calculator.production.advice.sheep.normal".localized
                }

                enum Chicken {
                    static let low = "tools.calculator.production.advice.chicken.low".localized
                    static let high = "tools.calculator.production.advice.chicken.high".localized
                    static let normal = "tools.calculator.production.advice.chicken.normal".localized
                }
            }
        }

        // 农场计算器工具
        enum Calculator {
            static let feed = "tools.calculator.feed".localized
            static let breeding = "tools.calculator.breeding".localized
            static let cost = "tools.calculator.cost".localized
            static let land = "tools.calculator.land".localized
            static let production = "tools.calculator.production".localized

            // 描述
            static let feedDescription = "tools.calculator.feed.description".localized
            static let breedingDescription = "tools.calculator.breeding.description".localized
            static let costDescription = "tools.calculator.cost.description".localized
            static let landDescription = "tools.calculator.land.description".localized
            static let productionDescription = "tools.calculator.production.description".localized

            // 饲料计算器
            static let feedTitle = "tools.calculator.feed.title".localized
            static let animalType = "tools.calculator.animalType".localized
            static let animalAge = "tools.calculator.animalAge".localized
            static let animalWeight = "tools.calculator.animalWeight".localized
            static let animalCount = "tools.calculator.animalCount".localized
            static let feedRequirement = "tools.calculator.feedRequirement".localized
            static let feedType = "tools.calculator.feedType".localized
            static let calculate = "tools.calculator.calculate".localized
            static let result = "tools.calculator.result".localized
            static let feedingAdvice = "tools.calculator.feedingAdvice".localized

            // 繁殖计算器
            static let breedingTitle = "tools.calculator.breeding.title".localized
            static let breedingDate = "tools.calculator.breeding.date".localized
            static let dueDate = "tools.calculator.breeding.dueDate".localized
            static let remainingDays = "tools.calculator.breeding.remainingDays".localized
            static let gestationProgress = "tools.calculator.breeding.gestationProgress".localized
            static let keyDates = "tools.calculator.breeding.keyDates".localized
            static let progressCard = "tools.calculator.breeding.progressCard".localized
            static let dueDateCard = "tools.calculator.breeding.dueDateCard".localized
            static let keyDatesCard = "tools.calculator.breeding.keyDatesCard".localized
            static let managementTips = "tools.calculator.breeding.managementTips".localized

            // 繁殖关键日期
            static let breedingDateTitle = "tools.calculator.breeding.breedingDateTitle".localized
            static let breedingDateDesc = "tools.calculator.breeding.breedingDateDesc".localized
            static let pregnancyConfirmation = "tools.calculator.breeding.pregnancyConfirmation".localized
            static let pregnancyConfirmationDesc = "tools.calculator.breeding.pregnancyConfirmationDesc".localized
            static let dryPeriodStart = "tools.calculator.breeding.dryPeriodStart".localized
            static let dryPeriodStartDesc = "tools.calculator.breeding.dryPeriodStartDesc".localized
            static let nestingPreparation = "tools.calculator.breeding.nestingPreparation".localized
            static let nestingPreparationDesc = "tools.calculator.breeding.nestingPreparationDesc".localized
            static let vaccination = "tools.calculator.breeding.vaccination".localized
            static let vaccinationDesc = "tools.calculator.breeding.vaccinationDesc".localized
            static let dueDateTitle = "tools.calculator.breeding.dueDateTitle".localized
            static let dueDateDesc = "tools.calculator.breeding.dueDateDesc".localized

            // 繁殖建议
            static let cattleAdvice = "tools.calculator.breeding.advice.cattle".localized
            static let sheepAdvice = "tools.calculator.breeding.advice.sheep".localized
            static let pigAdvice = "tools.calculator.breeding.advice.pig".localized
            static let horseAdvice = "tools.calculator.breeding.advice.horse".localized
            static let goatAdvice = "tools.calculator.breeding.advice.goat".localized
            static let defaultBreedingAdvice = "tools.calculator.breeding.advice.default".localized

            // 成本分析器
            static let costTitle = "tools.calculator.cost.title".localized
            static let raisingPeriod = "tools.calculator.cost.raisingPeriod".localized
            static let monthlyCostEstimate = "tools.calculator.cost.monthlyCostEstimate".localized
            static let feedCost = "tools.calculator.cost.feedCost".localized
            static let vetCost = "tools.calculator.cost.vetCost".localized
            static let laborCost = "tools.calculator.cost.laborCost".localized
            static let otherCost = "tools.calculator.cost.otherCost".localized
            static let expectedSalePrice = "tools.calculator.cost.expectedSalePrice".localized
            static let totalCost = "tools.calculator.cost.totalCost".localized
            static let totalRevenue = "tools.calculator.cost.totalRevenue".localized
            static let profit = "tools.calculator.cost.profit".localized
            static let roi = "tools.calculator.cost.roi".localized
            static let costBreakdown = "tools.calculator.cost.costBreakdown".localized
            static let profitAnalysis = "tools.calculator.cost.profitAnalysis".localized
            static let monthlyFeedCost = "tools.calculator.cost.monthlyFeedCost".localized
            static let monthlyVetCost = "tools.calculator.cost.monthlyVetCost".localized
            static let monthlyLaborCost = "tools.calculator.cost.monthlyLaborCost".localized
            static let monthlyOtherCost = "tools.calculator.cost.monthlyOtherCost".localized
            static let expectedRevenue = "tools.calculator.cost.expectedRevenue".localized
            static let salePrice = "tools.calculator.cost.salePrice".localized
            static let analysisResults = "tools.calculator.cost.analysisResults".localized
            static let perAnimalSalePrice = "tools.calculator.cost.perAnimalSalePrice".localized
            static let laborShort = "tools.calculator.cost.laborShort".localized
            static let roiLabel = "tools.calculator.cost.roiLabel".localized

            // 土地管理
            static let landTitle = "tools.calculator.land.title".localized
            static let landArea = "tools.calculator.land.area".localized
            static let resourceType = "tools.calculator.land.resourceType".localized
            static let fertilizer = "tools.calculator.land.fertilizer".localized
            static let seed = "tools.calculator.land.seed".localized
            static let water = "tools.calculator.land.water".localized
            static let amount = "tools.calculator.land.amount".localized
            static let perUnitArea = "tools.calculator.land.perUnitArea".localized
            static let fertilizerType = "tools.calculator.land.fertilizerType".localized
            static let seedType = "tools.calculator.land.seedType".localized
            static let cropType = "tools.calculator.land.cropType".localized
            static let areaUnit = "tools.calculator.land.areaUnit".localized
            static let totalAmount = "tools.calculator.land.totalAmount".localized
            static let calculationResult = "tools.calculator.land.calculationResult".localized
            static let perUnitUsage = "tools.calculator.land.perUnitUsage".localized
            static let recommendedRange = "tools.calculator.land.recommendedRange".localized
            static let totalCoverage = "tools.calculator.land.totalCoverage".localized
            static let usageAdvice = "tools.calculator.land.usageAdvice".localized
            static let lowUsageWarning = "tools.calculator.land.lowUsageWarning".localized
            static let highUsageWarning = "tools.calculator.land.highUsageWarning".localized

            // 生产追踪
            static let productionTitle = "tools.calculator.production.title".localized
            static let productionAmount = "tools.calculator.production.productionAmount".localized
            static let date = "tools.calculator.production.date".localized
            static let addRecord = "tools.calculator.production.addRecord".localized
            static let records = "tools.calculator.production.records".localized
            static let averagePerAnimal = "tools.calculator.production.averagePerAnimal".localized
            static let totalProduction = "tools.calculator.production.totalProduction".localized
            static let expectedRange = "tools.calculator.production.expectedRange".localized
            static let noRecords = "tools.calculator.production.noRecords".localized

            // 单位文本
            static let days = "tools.calculator.unit.days".localized
            static let months = "tools.calculator.unit.months".localized
            static let kg = "tools.calculator.unit.kg".localized
            static let weekly = "tools.calculator.unit.weekly".localized
            static let monthly = "tools.calculator.unit.monthly".localized
            static let singleAnimal = "tools.calculator.singleAnimal".localized
            static let allAnimals = "tools.calculator.allAnimals".localized

            // 动物类型
            enum Animal {
                static let cattle = "tools.calculator.animal.cattle".localized
                static let sheep = "tools.calculator.animal.sheep".localized
                static let pig = "tools.calculator.animal.pig".localized
                static let chicken = "tools.calculator.animal.chicken".localized
                static let duck = "tools.calculator.animal.duck".localized
                static let horse = "tools.calculator.animal.horse".localized
                static let goat = "tools.calculator.animal.goat".localized
            }

            // 饲料类型
            enum Feed {
                static let grain = "tools.calculator.feed.grain".localized
                static let hay = "tools.calculator.feed.hay".localized
                static let silage = "tools.calculator.feed.silage".localized
                static let concentrate = "tools.calculator.feed.concentrate".localized
            }

            // 土地管理
            enum Land {
                static let compound = "tools.calculator.land.compound".localized
                static let organic = "tools.calculator.land.organic".localized
                static let nitrogen = "tools.calculator.land.nitrogen".localized
                static let phosphorus = "tools.calculator.land.phosphorus".localized
                static let potassium = "tools.calculator.land.potassium".localized
                static let corn = "tools.calculator.land.corn".localized
                static let wheat = "tools.calculator.land.wheat".localized
                static let rice = "tools.calculator.land.rice".localized
                static let soybean = "tools.calculator.land.soybean".localized
                static let alfalfa = "tools.calculator.land.alfalfa".localized
                static let grass = "tools.calculator.land.grass".localized
                static let mu = "tools.calculator.land.unit.mu".localized
                static let hectare = "tools.calculator.land.unit.hectare".localized
                static let acre = "tools.calculator.land.unit.acre".localized
                static let kg = "tools.calculator.land.unit.kg".localized
                static let cubicMeter = "tools.calculator.land.unit.cubicMeter".localized

                // 使用建议
                enum Advice {
                    // 肥料使用建议
                    static let nitrogen = "tools.calculator.land.advice.nitrogen".localized
                    static let phosphorus = "tools.calculator.land.advice.phosphorus".localized
                    static let potassium = "tools.calculator.land.advice.potassium".localized
                    static let compound = "tools.calculator.land.advice.compound".localized
                    static let organic = "tools.calculator.land.advice.organic".localized

                    // 种子播种建议
                    static let corn = "tools.calculator.land.advice.corn".localized
                    static let wheat = "tools.calculator.land.advice.wheat".localized
                    static let rice = "tools.calculator.land.advice.rice".localized
                    static let soybean = "tools.calculator.land.advice.soybean".localized
                    static let alfalfa = "tools.calculator.land.advice.alfalfa".localized

                    // 灌溉建议
                    static let water = "tools.calculator.land.advice.water".localized
                }
            }

            // 单位名称
            enum Unit {
                // 面积单位
                static let squareMeter = "tools.converter.unit.squareMeter".localized
                static let mu = "tools.converter.unit.mu".localized
                static let hectare = "tools.converter.unit.hectare".localized
                static let squareKilometer = "tools.converter.unit.squareKilometer".localized
                static let acre = "tools.converter.unit.acre".localized

                // 重量单位
                static let kilogram = "tools.converter.unit.kilogram".localized
                static let gram = "tools.converter.unit.gram".localized
                static let jin = "tools.converter.unit.jin".localized
                static let ton = "tools.converter.unit.ton".localized
                static let pound = "tools.converter.unit.pound".localized

                // 容量单位
                static let liter = "tools.converter.unit.liter".localized
                static let milliliter = "tools.converter.unit.milliliter".localized
                static let cubicMeter = "tools.converter.unit.cubicMeter".localized
                static let gallon = "tools.converter.unit.gallon".localized

                // 长度单位
                static let meter = "tools.converter.unit.meter".localized
                static let centimeter = "tools.converter.unit.centimeter".localized
                static let kilometer = "tools.converter.unit.kilometer".localized
                static let foot = "tools.converter.unit.foot".localized
                static let inch = "tools.converter.unit.inch".localized

                // 温度单位
                static let celsius = "tools.converter.unit.celsius".localized
                static let fahrenheit = "tools.converter.unit.fahrenheit".localized
                static let kelvin = "tools.converter.unit.kelvin".localized
            }

            // 饲料建议
            enum Advice {
                // 牛的饲料建议
                static let cattleYoung = "tools.calculator.advice.cattle.young".localized
                static let cattleGrowing = "tools.calculator.advice.cattle.growing".localized
                static let cattleAdult = "tools.calculator.advice.cattle.adult".localized

                // 其他动物饲料建议
                static let sheep = "tools.calculator.advice.sheep".localized
                static let pigYoung = "tools.calculator.advice.pig.young".localized
                static let pigAdult = "tools.calculator.advice.pig.adult".localized
                static let chicken = "tools.calculator.advice.chicken".localized
                static let duck = "tools.calculator.advice.duck".localized
                static let horse = "tools.calculator.advice.horse".localized
                static let defaultAdvice = "tools.calculator.advice.default".localized
            }
        }
    }
    
    // Errors & Alerts
    enum Error {
        static let required = "error.required".localized
        static let title = "error.title".localized
    }
    
    // Profile
    enum Profile {
        static let title = "profile.title".localized
        static let farmManagement = "profile.farm.management".localized
        static let settings = "profile.settings".localized
        static let help = "profile.help".localized
        static let dataBackup = "profile.data.backup".localized
        static let dataExport = "profile.data.export".localized
        static let statistics = "profile.statistics".localized
        static let notification = "profile.notification".localized
        static let language = "profile.language".localized
        static let privacy = "profile.privacy".localized
        static let about = "profile.about".localized
        static let version = "profile.version".localized
        static let helpUsage = "profile.help.usage".localized
        static let rate = "profile.rate".localized
        static let contact = "profile.contact".localized
        static let userTitle = "profile.user.title".localized
        static let userDays = "profile.user.days".localized
        static let userPhone = "profile.user.phone".localized
        static let totalAnimals = "profile.total.animals".localized
        static let animalsInstock = "profile.animals.instock".localized
        static let animalsAdded = "profile.animals.added".localized
        static let edit = "profile.edit".localized
        static let back = "profile.back".localized
    }
    
    // Notification
    enum Notification {
        static let title = "notification.title".localized
        static let daily = "notification.daily".localized
        static let time = "notification.time".localized
        static let systemSettings = "notification.system.settings".localized
        static let systemOpen = "notification.system.open".localized
        static let about = "notification.about".localized
        static let content = "notification.content".localized
        static let message = "notification.message".localized
        static let permissionDenied = "notification.permission.denied".localized
        static let permissionMessage = "notification.permission.message".localized
        static let gotoSettings = "notification.goto.settings".localized
        static let systemHint = "notification.system.hint".localized
    }
    
    // Email
    enum Email {
        static let contact = "email.contact".localized
        static let copy = "email.copy".localized
        static let copied = "email.copied".localized
        static let close = "email.close".localized
    }
    
    // Usage Guide
    enum UsageGuide {
        static let title = "usage.guide.title".localized
        static let close = "usage.guide.close".localized
        static let content1 = "usage.guide.content.1".localized
        static let content2 = "usage.guide.content.2".localized
        static let content3 = "usage.guide.content.3".localized
        static let content4 = "usage.guide.content.4".localized
        static let content5 = "usage.guide.content.5".localized
        static let content6 = "usage.guide.content.6".localized
    }
} 