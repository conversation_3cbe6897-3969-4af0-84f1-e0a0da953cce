// General
"app.name" = "Veehouderij Beheerder";
"cancel" = "Annuleren";
"save" = "Opslaan";
"update" = "Bijwerken";
"edit" = "Bewerken";
"delete" = "Verwijderen";
"confirm" = "Bevestigen";
"done" = "Klaar";
"unknown" = "Onbekend";
"required" = "Vereist";

// Tab & Navigation
"tab.home" = "Home";
"tab.add" = "Toevoegen";
"tab.profile" = "Profiel";
"tab.todo" = "Taken";
"tab.search" = "Zoeken";
"tab.tools" = "Gereedschap";
"nav.myFarm" = "Mijn Boerderij";

// Todo
"todo.title" = "Boerderij Taken";
"todo.add.placeholder" = "Voeg een boerderijtak toe...";
"todo.hide.completed" = "Verberg voltooide";
"todo.show.completed" = "Toon voltooide";
"todo.empty" = "Nog geen taken";
"todo.empty.desc" = "Voeg enkele boerderijwerktaken toe";
"todo.all.completed" = "Alle taken voltooid";
"todo.view.completed" = "Bekijk voltooide taken";
"todo.example.task1" = "Controleer rundveehek";
"todo.example.task2" = "Desinfecteer varkenshok";
"todo.example.task3" = "Voer aanvullen";

// Home
"home.empty.welcome" = "Welkom bij uw boerderij!";
"home.empty.noAnimals" = "U heeft nog geen vee toegevoegd";
"home.empty.startAdding" = "Klik op de knop hieronder om uw eerste vee te registreren";
"home.empty.addFirst" = "Eerste vee toevoegen";
"home.features.title" = "Boerderijbeheer functies";
"home.features.records" = "Recordbeheer";
"home.features.records.desc" = "Registreer gedetailleerde informatie over uw vee, inclusief ras, leeftijd, gezondheidsstatus, enz.";
"home.features.search" = "Snel zoeken";
"home.features.search.desc" = "Vind snel het vee dat u wilt controleren via ID, ras en andere trefwoorden";
"home.features.filter" = "Slim filteren";
"home.features.filter.desc" = "Filter op soort, geslacht, status en andere voorwaarden voor snel gecategoriseerd beheer";
"home.features.stats" = "Gegevensstatistieken";
"home.features.stats.desc" = "Bekijk realtime statistieken over in voorraad, te koop, verkocht en andere statussen";
"home.tips.title" = "Gebruikstips";
"home.tips.photos" = "Neem foto's van elk dier voor gemakkelijke identificatie en beheer";
"home.tips.ids" = "Gebruik een consistent nummeringssysteem, zoals 'Rundvee001', 'Schaap001', enz.";
"home.tips.updates" = "Update regelmatig de status van vee om de nauwkeurigheid van gegevens te behouden";
"home.tips.features" = "Maak volledig gebruik van zoek- en filterfuncties om de beheersefficiëntie te verbeteren";

// Filter
"filter.title" = "Vee filteren";
"filter.applied" = "Filters toegepast";
"filter.clear" = "Wissen";
"filter.clearAll" = "Alle filters wissen";
"filter.apply" = "Filters toepassen";
"filter.reset" = "Filters resetten";
"filter.noResults" = "Geen vee dat aan de filtercriteria voldoet";
"filter.noResults.help" = "Probeer filtercriteria aan te passen of alle filters te wissen";
"filter.all" = "Alle";
"filter.species" = "Soorten";
"filter.gender" = "Geslacht";
"filter.status" = "Status";
"filter.age" = "Leeftijdsbereik";
"filter.source" = "Bron";
"filter.quick" = "Snelle filters";
"filter.quick.healthy" = "Gezond vee";
"filter.quick.healthy.desc" = "(Actieve status)";
"filter.quick.forSale" = "Vee te koop";
"filter.quick.forSale.desc" = "(Te koop status)";
"filter.quick.young" = "Jong vee";
"filter.quick.young.desc" = "(Onder 1 jaar)";

// Animal Properties
"animal.id" = "Unieke ID";
"animal.species" = "Soort";
"animal.breed" = "Ras";
"animal.gender" = "Geslacht";
"animal.birthDate" = "Geboortedatum";
"animal.age" = "Leeftijd";
"animal.source" = "Bron";
"animal.purchaseDate" = "Aankoopdatum";
"animal.purchasePrice" = "Aankoopprijs";
"animal.status" = "Status";
"animal.notes" = "Notities";
"animal.photo" = "Veefoto";
"animal.addPhoto" = "Foto toevoegen";

// Species
"species.cattle" = "Rundvee";
"species.sheep" = "Schaap";
"species.pig" = "Varken";
"species.horse" = "Paard";
"species.chicken" = "Kip";
"species.duck" = "Eend";
"species.goose" = "Gans";
"species.other" = "Andere";

// Gender
"gender.male" = "Mannelijk";
"gender.female" = "Vrouwelijk";
"gender.castrated" = "Gecastreerd";

// Source
"source.selfBred" = "Zelf gefokt";
"source.purchased" = "Gekocht";

// Status
"status.active" = "Actief";
"status.forSale" = "Te koop";
"status.sold" = "Verkocht";
"status.deceased" = "Overleden";
"status.other" = "Andere";

// Age Range
"ageRange.young" = "0-1 jaar";
"ageRange.middle" = "1-3 jaar";
"ageRange.adult" = "Boven 3 jaar";

// Add/Edit Animal
"add.title" = "Vee toevoegen";
"edit.title" = "Vee bewerken";
"add.id.hint" = "bijv. oormerk nummer, aangepaste ID";
"add.breed.placeholder" = "Voer ras in";
"add.price.placeholder" = "Voer prijs in";
"add.notes.placeholder" = "Voer notities in";
"add.requiredFields" = "* Verplichte velden";

// Detail View
"detail.basicInfo" = "Basisinformatie";
"detail.statusInfo" = "Statusinformatie";
"detail.notes" = "Notities";
"detail.deleteConfirm" = "Verwijderen bevestigen";
"detail.deleteMessage" = "Gegevens kunnen na verwijdering niet worden hersteld. Bevestigt u verwijdering?";

// Statistics
"stats.inStock" = "In voorraad";
"stats.forSale" = "Te koop";
"stats.sold" = "Verkocht";
"stats.deceased" = "Overleden";

// Search
"search.placeholder" = "Zoek op ID, ras...";
"search.noResults" = "Geen resultaten gevonden";
"search.recentSearches" = "Recente zoekopdrachten";
"search.clearHistory" = "Geschiedenis wissen";
"search.hotSearches" = "Populaire zoekopdrachten";
"search.quickSearch" = "Snelle zoektags";

// Errors & Alerts
"error.required" = "Vul verplichte velden in";
"error.title" = "Melding";

// Language Selection
"language.title" = "Selecteer taal";
"language.close" = "Sluiten";
"language.changed" = "Taal gewijzigd";
"language.restart" = "Start de app opnieuw op om taalwijzigingen volledig toe te passen";
"language.ok" = "OK";

// Profile
"profile.title" = "Profiel";
"profile.farm.management" = "Boerderijbeheer";
"profile.settings" = "Instellingen";
"profile.help" = "Help & Feedback";
"profile.data.backup" = "Gegevensback-up";
"profile.data.export" = "Gegevensexport";
"profile.statistics" = "Statistiekrapport";
"profile.notification" = "Meldingsinstellingen";
"profile.language" = "Taalinstellingen";
"profile.privacy" = "Privacybeleid";
"profile.about" = "Versie";
"profile.version" = "Versie %@";
"profile.help.usage" = "Gebruikshulp";
"profile.rate" = "Beoordeel ons";
"profile.contact" = "Neem contact op met ondersteuning";
"profile.user.title" = "Boer";
"profile.user.days" = "Dagen gebruikt: %d dagen";
"profile.user.phone" = "Telefoon: %@";
"profile.total.animals" = "Totaal vee";
"profile.animals.instock" = "In voorraad";
"profile.animals.added" = "Deze maand toegevoegd";
"profile.edit" = "Profiel bewerken";
"profile.back" = "Terug";

// Notification Settings
"notification.title" = "Meldingsinstellingen";
"notification.daily" = "Dagelijkse herinnering";
"notification.time" = "Herinneringstijd";
"notification.system.settings" = "Systeeminstellingen";
"notification.system.open" = "Open systeemmeldingsinstellingen";
"notification.about" = "Over meldingen";
"notification.content" = "Meldingsinhoud";
"notification.message" = "Dagelijkse herinnering zal u op de ingestelde tijd melden: \"Tijd om uw dieren te beheren!\"";
"notification.permission.denied" = "Meldingstoestemming geweigerd";
"notification.permission.message" = "Sta meldingen toe voor deze app in apparaatinstellingen";
"notification.goto.settings" = "Ga naar instellingen";
"notification.system.hint" = "Als u geen meldingen ontvangt, zorg ervoor dat Veehouderij Beheerder is toegestaan om meldingen te verzenden in systeeminstellingen";

// Email Popup
"email.contact" = "Contact opnemen met ondersteuning";
"email.copy" = "E-mail kopiëren";
"email.copied" = "Gekopieerd";
"email.close" = "Sluiten";

// Usage Guide
"usage.guide.title" = "Gebruiksgids";
"usage.guide.close" = "Sluiten";
"usage.guide.content.1" = "1. Startpagina\n• Bekijk alle veelijst\n• Gebruik zoeken om snel specifiek vee te vinden\n• Gebruik filters om vee per voorwaarde te bekijken";
"usage.guide.content.2" = "2. Vee toevoegen\n• Klik op de knop 'Toevoegen' onderaan\n• Vul basisgegevens van het vee in\n• Voeg optioneel foto's toe\n• Klik op opslaan wanneer klaar";
"usage.guide.content.3" = "3. Veebeheer\n• Klik op veekaart om details te bekijken\n• Bewerk of verwijder vee-informatie\n• Update veestatus tijdig";
"usage.guide.content.4" = "4. Takenlijst\n• Voeg werkherinneringen toe op de pagina 'Taken'\n• Markeer als voltooid wanneer klaar\n• Optie om voltooide taken te verbergen";
"usage.guide.content.5" = "5. Meldingen\n• Schakel meldingen in bij instellingen\n• Stel dagelijkse herinneringstijd in\n• Mis nooit belangrijk werk";
"usage.guide.content.6" = "6. Gegevensbeveiliging\n• Controleer regelmatig gegevensnauwkeurigheid\n• Sla belangrijke informatie tijdig op\n• Neem contact op met ondersteuning bij problemen";

// Tools
"tools.title" = "Gereedschap";
"tools.counter" = "Teller";
"tools.calculator" = "Boerderij Calculator";
"tools.weather" = "Weer";
"tools.marketPrice" = "Marktprijs";
"tools.timer" = "Timer";
"tools.notes" = "Notities";
"tools.calendar" = "Kalender";
"tools.converter" = "Eenheidsconverter";
"tools.medicine" = "Medicijnen";
"tools.basicTools" = "Basisgereedschap";
"tools.suggestions" = "Gebruikerssuggesties";
"tools.suggestNewTool" = "Nieuw gereedschap voorstellen";

// Counter Tool
"tools.counter.start" = "Nieuwe telling starten";
"tools.counter.reset" = "Resetten";
"tools.counter.complete" = "Telling voltooien";
"tools.counter.counting" = "Tellen";
"tools.counter.startTime" = "Starttijd: %@";
"tools.counter.new" = "Start een nieuwe telling";
"tools.counter.history" = "Telgeschiedenis";
"tools.counter.save" = "Telling opslaan";
"tools.counter.name" = "Tellnaam";
"tools.counter.note" = "Notities";
"tools.counter.date" = "Datum";
"tools.counter.count" = "Telling";
"tools.counter.noHistory" = "Nog geen telgeschiedenis";
"tools.counter.deleteHistory" = "Geschiedenis verwijderen";

// Counter Tool Record
"tools.counter.record.title" = "Record details";
"tools.counter.record.details" = "Record details";
"tools.counter.record.count" = "Telling";
"tools.counter.record.namePlaceholder" = "Voer tellnaam in";
"tools.counter.record.notePlaceholder" = "Voer notities in";

// Counter Tool History
"tools.counter.history.title" = "Telgeschiedenis";
"tools.counter.history.empty" = "Geen telgeschiedenis";
"tools.counter.history.countLabel" = "Telling: %lld";
"tools.counter.history.timeLabel" = "Tijd: %@ - %@";
"tools.counter.history.noteLabel" = "Notities: %@";

// Unit Converter Tool
"tools.converter.value" = "Waarde";
"tools.converter.from" = "Van";
"tools.converter.to" = "Naar";
"tools.converter.result" = "Conversieresultaat";
"tools.converter.reference" = "Algemene conversiereferentie";
"tools.converter.placeholder" = "Voer waarde in";
"tools.converter.selectUnit" = "Selecteer eenheid";

// Converter Categories
"tools.converter.area" = "Oppervlakte";
"tools.converter.weight" = "Gewicht";
"tools.converter.volume" = "Volume";
"tools.converter.length" = "Lengte";
"tools.converter.temperature" = "Temperatuur";

// Converter Units
"tools.converter.unit.squareMeter" = "Vierkante meter";
"tools.converter.unit.mu" = "Mu";
"tools.converter.unit.hectare" = "Hectare";
"tools.converter.unit.squareKilometer" = "Vierkante kilometer";
"tools.converter.unit.acre" = "Acre";

"tools.converter.unit.kilogram" = "Kilogram";
"tools.converter.unit.gram" = "Gram";
"tools.converter.unit.jin" = "Jin";
"tools.converter.unit.ton" = "Ton";
"tools.converter.unit.pound" = "Pond";

"tools.converter.unit.liter" = "Liter";
"tools.converter.unit.milliliter" = "Milliliter";
"tools.converter.unit.cubicMeter" = "Kubieke meter";
"tools.converter.unit.gallon" = "Gallon";

"tools.converter.unit.meter" = "Meter";
"tools.converter.unit.centimeter" = "Centimeter";
"tools.converter.unit.kilometer" = "Kilometer";
"tools.converter.unit.foot" = "Voet";
"tools.converter.unit.inch" = "Inch";

"tools.converter.unit.celsius" = "Celsius";
"tools.converter.unit.fahrenheit" = "Fahrenheit";
"tools.converter.unit.kelvin" = "Kelvin";

// Converter Reference
"tools.converter.reference.muToSqM" = "1 mu ≈ 666,67 vierkante meter";
"tools.converter.reference.hectareToSqM" = "1 hectare = 10.000 vierkante meter";
"tools.converter.reference.acreToSqM" = "1 acre ≈ 4.046,86 vierkante meter";
"tools.converter.reference.kgToJin" = "1 kilogram = 2 jin";
"tools.converter.reference.tonToKg" = "1 ton = 1.000 kilogram";
"tools.converter.reference.poundToKg" = "1 pond ≈ 0,453592 kilogram";
"tools.converter.reference.literToMl" = "1 liter = 1.000 milliliter";
"tools.converter.reference.gallonToLiter" = "1 gallon (VS) ≈ 3,785 liter";
"tools.converter.reference.kmToM" = "1 kilometer = 1.000 meter";
"tools.converter.reference.footToM" = "1 voet ≈ 0,3048 meter";
"tools.converter.reference.inchToCm" = "1 inch = 2,54 centimeter";
"tools.converter.reference.celsiusToFahrenheit" = "0°C = 32°F (vriespunt)";
"tools.converter.reference.fahrenheitToCelsius" = "32°F = 0°C (vriespunt)";
"tools.converter.reference.celsiusToKelvin" = "0°C = 273,15K (absoluut nulpunt + 273,15)";

// Weather Tool
"tools.weather.locationPermissionTitle" = "Locatietoestemming vereist";
"tools.weather.needsPermissionMessage" = "We hebben toegang tot uw locatie nodig om weerinformatie voor uw gebied te verstrekken. Sta locatietoegang toe in instellingen.";
"tools.weather.farmingSuggestionTitle" = "Landbouwsuggesties";
"tools.weather.loading" = "Weergegevens laden...";
"tools.weather.errorUnknown" = "Onbekende fout";
"tools.weather.errorLocation" = "Kan geen toegang krijgen tot locatietoestemming";
"tools.weather.refresh" = "Vernieuwen";
"tools.weather.hourlyForecast" = "Uurlijkse voorspelling";
"tools.weather.dailyForecast" = "Dagelijkse voorspelling";
"tools.weather.farmingSuggestions" = "Landbouwsuggesties";
"tools.weather.goToSettings" = "Ga naar instellingen";
"tools.weather.needsPermission" = "Locatietoestemming is nodig om weer voor uw gebied te krijgen";
"tools.weather.locationPermission" = "Locatietoestemming";
"tools.weather.cancel" = "Annuleren";
"tools.weather.feelsLike" = "Voelt als";
"tools.weather.humidity" = "Luchtvochtigheid";
"tools.weather.wind" = "Wind";
"tools.weather.uvIndex" = "UV-index";
"tools.weather.visibility" = "Zichtbaarheid";
"tools.weather.precipitation" = "Neerslag";
"tools.weather.today" = "Vandaag";
"tools.weather.tomorrow" = "Morgen";
"tools.weather.noData" = "Geen weergegevens beschikbaar";

// Weather Tool Additional Keys
"tools.weather.errorLocationUnavailable" = "Locatie-informatie niet beschikbaar";
"tools.weather.errorManagerFailed" = "Locatiemanager fout";
"tools.weather.errorFetch" = "Ophalen van weerinformatie mislukt";
"tools.weather.gettingLocation" = "Locatie ophalen...";
"tools.weather.unknownLocation" = "Onbekende locatie";
"tools.weather.noSpecificSuggestionMessage" = "Geen specifieke landbouwsuggesties op dit moment";

// Weather Tool - UV Index
"tools.weather.uv.low" = "Laag";
"tools.weather.uv.moderate" = "Matig";
"tools.weather.uv.high" = "Hoog";
"tools.weather.uv.veryHigh" = "Zeer hoog";
"tools.weather.uv.extreme" = "Extreem";

// Weather Tool - Farming Suggestions
"tools.weather.suggestion.uvHigh" = "UV-index is hoog, neem zonbeschermingsmaatregelen en bescherm vee";
"tools.weather.suggestion.heavyRainToday" = "Zware regen vandaag, zorg voor regenbescherming van vee";
"tools.weather.suggestion.strongWindToday" = "Sterke wind vandaag, aanbevolen om schuilfaciliteiten te beveiligen";
"tools.weather.suggestion.rainNext3Days" = "Regen verwacht in de komende drie dagen, bereid regenbeschermingsmaatregelen voor";
"tools.weather.suggestion.heatWave" = "Hoge temperaturen verwacht in komende dagen, handhaaf koelingsmaatregelen";
"tools.weather.suggestion.coldSpell" = "Lage temperaturen verwacht in komende dagen, zorg voor warmtebescherming";
"tools.weather.suggestion.irrigation" = "Geen recente regen, overweeg irrigatie van weilanden";

// Farm Calculator Tool
"tools.calculator.feed" = "Voedercalculator";
"tools.calculator.breeding" = "Fokkerijcalculator";
"tools.calculator.cost" = "Kostenanalyse";
"tools.calculator.land" = "Landbeheer";
"tools.calculator.production" = "Productietracker";

// Farm Calculator Descriptions
"tools.calculator.feed.description" = "Bereken dagelijkse voederbehoeften op basis van diertype, leeftijd en gewicht";
"tools.calculator.breeding.description" = "Bereken draagtijd en verwachte bevallingsdatum";
"tools.calculator.cost.description" = "Analyseer voeder-, verzorgings- en verkoopkosten";
"tools.calculator.land.description" = "Bereken kunstmestbehoeften en zaaipercentages";
"tools.calculator.production.description" = "Volg melkproductie en eileggen";

// Feed Calculator
"tools.calculator.feed.title" = "Voederbehoeftecalculator";
"tools.calculator.animalType" = "Diertype";
"tools.calculator.animalAge" = "Leeftijd (maanden)";
"tools.calculator.animalWeight" = "Gewicht (kg)";
"tools.calculator.animalCount" = "Aantal dieren";
"tools.calculator.feedRequirement" = "Dagelijkse voederbehoefte";
"tools.calculator.feedType" = "Voedertype";
"tools.calculator.calculate" = "Berekenen";
"tools.calculator.result" = "Resultaat";
"tools.calculator.feedingAdvice" = "Voederadvies";

// Breeding Calculator
"tools.calculator.breeding.title" = "Fokkerijcalculator";
"tools.calculator.breeding.date" = "Fokdatum";
"tools.calculator.breeding.dueDate" = "Verwachte bevallingsdatum";
"tools.calculator.breeding.remainingDays" = "Resterende dagen";
"tools.calculator.breeding.gestationProgress" = "Draagtijdvoortgang";
"tools.calculator.breeding.keyDates" = "Belangrijke data";
"tools.calculator.breeding.progressCard" = "Draagtijdvoortgang";
"tools.calculator.breeding.dueDateCard" = "Bevallingsdatuminformatie";
"tools.calculator.breeding.keyDatesCard" = "Belangrijke datumherinneringen";
"tools.calculator.breeding.managementTips" = "Fokkerijbeheertips";

// Breeding Key Dates
"tools.calculator.breeding.breedingDateTitle" = "Fokdatum";
"tools.calculator.breeding.breedingDateDesc" = "Datum van paring of kunstmatige inseminatie";
"tools.calculator.breeding.pregnancyConfirmation" = "Zwangerschapsbevestiging";
"tools.calculator.breeding.pregnancyConfirmationDesc" = "Aanbevolen tijd voor zwangerschapscontrole";
"tools.calculator.breeding.dryPeriodStart" = "Begin droogstand";
"tools.calculator.breeding.dryPeriodStartDesc" = "Stop met melken om koe voor te bereiden op kalven";
"tools.calculator.breeding.nestingPreparation" = "Nestvoorbereiding";
"tools.calculator.breeding.nestingPreparationDesc" = "Bereid kraamhok en omgeving voor zeug voor";
"tools.calculator.breeding.vaccination" = "Vaccinatie";
"tools.calculator.breeding.vaccinationDesc" = "Dien noodzakelijke vaccins toe aan drachtige merrie";
"tools.calculator.breeding.dueDateTitle" = "Bevallingsdatum";
"tools.calculator.breeding.dueDateDesc" = "Verwachte datum van bevalling";

// Breeding Advice
"tools.calculator.breeding.advice.cattle" = "De draagtijd van runderen is ongeveer 283 dagen (9,5 maanden). Speciale aandacht voor voeding is nodig in de laatste twee maanden van de zwangerschap. Bereid een schone, ruime kalverruimte voor. Stop met melken voor het kalven (droogstand) om het lichaam van de koe voor te bereiden op kalven en de nieuwe lactatiecyclus.";
"tools.calculator.breeding.advice.sheep" = "De draagtijd van schapen is ongeveer 152 dagen (5 maanden). Verhoog energie- en eiwitinname in de late zwangerschap. Schapen kunnen meestal zelf bevallen, maar bereid een schone stal en noodzakelijke bevallingshulpmiddelen voor voor het geval hulp nodig is.";
"tools.calculator.breeding.advice.pig" = "De draagtijd van varkens is ongeveer 114 dagen (3 maanden, 3 weken, 3 dagen). Zorg ervoor dat de zeug voldoende energie en eiwit krijgt in de late zwangerschap. Bereid een week voor de bevalling een schoon, warm kraamhok en warmtelampen voor om het comfort van pasgeboren biggen te waarborgen.";
"tools.calculator.breeding.advice.horse" = "De draagtijd van paarden is ongeveer 340 dagen (11 maanden). Drachtige merries hebben passende beweging en een uitgebalanceerd dieet nodig. Houd nauwlettend in de gaten in de weken voor het veulenen, bereid een schone stal voor en zorg ervoor dat veterinaire hulp beschikbaar is, omdat paardenbevalling speciale aandacht vereist.";
"tools.calculator.breeding.advice.goat" = "De draagtijd van geiten is ongeveer 150 dagen (5 maanden). Verhoog energie-inname in de late zwangerschap en verminder stress. Geiten kunnen meestal zelf bevallen, maar bereid een schone stal en noodzakelijke bevallingshulpmiddelen voor om hulp te bieden wanneer nodig.";
"tools.calculator.breeding.advice.default" = "Raadpleeg een dierenarts voor professioneel fokkerijbeheeradvies op basis van het specifieke ras en de conditie van het dier. Zorg voor uitgebalanceerde voeding tijdens de zwangerschap, verminder stress en bereid voor op de komende bevalling.";

// Calculator Units
"tools.calculator.unit.days" = "dagen";
"tools.calculator.unit.months" = "maanden";
"tools.calculator.unit.kg" = "kg";
"tools.calculator.unit.weekly" = "Wekelijkse behoefte:";
"tools.calculator.unit.monthly" = "Maandelijkse behoefte:";
"tools.calculator.singleAnimal" = "Enkel dier:";
"tools.calculator.allAnimals" = "Alle dieren:";

// Animal Types
"tools.calculator.animal.cattle" = "Rundvee";
"tools.calculator.animal.sheep" = "Schaap";
"tools.calculator.animal.pig" = "Varken";
"tools.calculator.animal.chicken" = "Kip";
"tools.calculator.animal.duck" = "Eend";
"tools.calculator.animal.horse" = "Paard";
"tools.calculator.animal.goat" = "Geit";

// Feed Types
"tools.calculator.feed.grain" = "Graan";
"tools.calculator.feed.hay" = "Hooi";
"tools.calculator.feed.silage" = "Kuilvoer";
"tools.calculator.feed.concentrate" = "Krachtvoer";

// Cost Analyzer
"tools.calculator.cost.title" = "Kostenanalyse";
"tools.calculator.cost.raisingPeriod" = "Fokperiode (maanden)";
"tools.calculator.cost.monthlyCostEstimate" = "Maandelijkse kostenschatting (€/maand)";
"tools.calculator.cost.feedCost" = "Voederkosten";
"tools.calculator.cost.vetCost" = "Dierenarts kosten";
"tools.calculator.cost.laborCost" = "Arbeidskosten";
"tools.calculator.cost.otherCost" = "Overige kosten";
"tools.calculator.cost.expectedSalePrice" = "Verwachte verkoopprijs (€/dier)";
"tools.calculator.cost.totalCost" = "Totale kosten";
"tools.calculator.cost.totalRevenue" = "Totale inkomsten";
"tools.calculator.cost.profit" = "Winst";
"tools.calculator.cost.roi" = "Rendement op investering";
"tools.calculator.cost.costBreakdown" = "Kostenverdeling";
"tools.calculator.cost.profitAnalysis" = "Winstanalyse";
"tools.calculator.cost.monthlyFeedCost" = "Maandelijkse voederkosten";
"tools.calculator.cost.monthlyVetCost" = "Maandelijkse dierenarts kosten";
"tools.calculator.cost.monthlyLaborCost" = "Maandelijkse arbeidskosten";
"tools.calculator.cost.monthlyOtherCost" = "Maandelijkse overige kosten";

// Kostenanalyse Extra Strings
"tools.calculator.cost.expectedRevenue" = "Verwachte inkomsten (€/dier)";
"tools.calculator.cost.salePrice" = "Verkoopprijs";
"tools.calculator.cost.analysisResults" = "Kostenanalyse resultaten";
"tools.calculator.cost.perAnimalSalePrice" = "Verkoopprijs per dier";
"tools.calculator.cost.laborShort" = "Arbeid";
"tools.calculator.cost.roiLabel" = "Rendement op investering (ROI)";

// Land Management
"tools.calculator.land.title" = "Landbeheer Calculator";
"tools.calculator.land.description" = "Bereken resourcegebruik per oppervlakte-eenheid";
"tools.calculator.land.area" = "Landoppervlakte";
"tools.calculator.land.resourceType" = "Resourcetype";
"tools.calculator.land.fertilizer" = "Kunstmest";
"tools.calculator.land.seed" = "Zaad";
"tools.calculator.land.water" = "Irrigatiewater";
"tools.calculator.land.amount" = "Totale hoeveelheid";
"tools.calculator.land.perUnitArea" = "Per oppervlakte-eenheid";
"tools.calculator.land.fertilizerType" = "Kunstmesttype";
"tools.calculator.land.seedType" = "Zaadtype";
"tools.calculator.land.cropType" = "Gewastype";
"tools.calculator.land.compound" = "Samengestelde kunstmest";
"tools.calculator.land.organic" = "Organische mest";
"tools.calculator.land.nitrogen" = "Stikstofmest";
"tools.calculator.land.phosphorus" = "Fosfaatmest";
"tools.calculator.land.potassium" = "Kaliummest";
"tools.calculator.land.corn" = "Maïs";
"tools.calculator.land.wheat" = "Tarwe";
"tools.calculator.land.rice" = "Rijst";
"tools.calculator.land.soybean" = "Sojaboon";
"tools.calculator.land.alfalfa" = "Luzerne";
"tools.calculator.land.grass" = "Weidegras";

// Land Management - Oppervlakte-eenheden
"tools.calculator.land.unit.mu" = "Mu";
"tools.calculator.land.unit.hectare" = "Hectare";
"tools.calculator.land.unit.acre" = "Acre";

// Land Management - Resource-eenheden
"tools.calculator.land.unit.kg" = "kg";
"tools.calculator.land.unit.cubicMeter" = "m³";

// Land Management - Interface tekst
"tools.calculator.land.areaUnit" = "Oppervlakte-eenheid";
"tools.calculator.land.totalAmount" = "Totale resourcehoeveelheid";
"tools.calculator.land.calculationResult" = "Berekeningsresultaat";
"tools.calculator.land.perUnitUsage" = "Gebruik per %@";
"tools.calculator.land.recommendedRange" = "Aanbevolen bereik";
"tools.calculator.land.totalCoverage" = "Totale dekkingsoppervlakte";
"tools.calculator.land.usageAdvice" = "Gebruiksadvies";
"tools.calculator.land.lowUsageWarning" = "Huidig gebruik ligt onder aanbevolen niveau, kan opbrengst beïnvloeden";
"tools.calculator.land.highUsageWarning" = "Huidig gebruik overschrijdt aanbevolen niveau, kan verspilling of milieueffecten veroorzaken";

// Land Management - Kunstmest gebruiksadvies
"tools.calculator.land.advice.nitrogen" = "Stikstofmest bevordert voornamelijk de groei van stengels en bladeren. Breng in meerdere doses aan om nutriëntenverlies door overmatige eenmalige toepassing te voorkomen. Het voorjaar groeiseizoen is de beste tijd voor toepassing. Let op dat overmatig gebruik kan leiden tot grondwaterverontreiniging.";
"tools.calculator.land.advice.phosphorus" = "Fosfaatmest helpt wortelontwikkeling en bloem-/vruchtontwikkeling te bevorderen. Breng aan in de onderste bodemlaag tijdens grondbewerking, omdat fosfor slecht beweeglijk is in de bodem. Fosforbeschikbaarheid is lager in zure bodems, dosering moet mogelijk worden aangepast.";
"tools.calculator.land.advice.potassium" = "Kaliummest verbetert gewasresistentie tegen ziekten en stress. Best toegepast voor bloei en vruchtvorming. Kalium spoelt gemakkelijk uit in zandgronden, mogelijk verhoogde toepassing of gesplitste toepassingen nodig.";
"tools.calculator.land.advice.compound" = "Samengestelde kunstmest bevat meerdere nutriënten, handig voor eenmalige toepassing. Kies geschikte NPK-verhoudingen op basis van bodemtestresultaten. Algemeen toegepast tijdens vroege gewasgroei om evenwichtige nutriëntenvoorziening te waarborgen.";
"tools.calculator.land.advice.organic" = "Organische mest verbetert bodemstructuur, verhoogt organische stofgehalte van de bodem en bevordert bodemicrobiële activiteit. Aanbevolen om te composteren voor gebruik. Organische mest geeft nutriënten langzaam af, heeft vroege toepassing nodig.";

// Land Management - Zaai advies
"tools.calculator.land.advice.corn" = "Maïszaad moet worden gezaaid wanneer de bodemtemperatuur stabiel boven 10°C is. Zaaidiepte ongeveer 3-5 cm, rijafstand 60-75 cm. Zorg voor gelijkmatig zaaien, vermijd gemiste plekken en dubbel zaaien.";
"tools.calculator.land.advice.wheat" = "Tarwe is geschikt voor herfst- of vroege voorjaarszaai. Zaaidiepte ongeveer 2-3 cm, rijafstand 15-20 cm. Behandel zaad voor ziektepreventie voor het zaaien, en verdicht de grond na het zaaien om kieming te bevorderen.";
"tools.calculator.land.advice.rice" = "Rijst gebruikt meestal zaailing transplantatie of directe zaai. Voor directe zaai, pas zaaipercentage aan volgens raskenmerken. Bij transplantatie, plant 2-3 zaailingen per gat, plantafstand ongeveer 15-20 cm, rijafstand ongeveer 25-30 cm.";
"tools.calculator.land.advice.soybean" = "Sojabonen zijn geschikt voor zaaien wanneer de bodemtemperatuur boven 15°C komt. Zaaidiepte ongeveer 2-4 cm, rijafstand 40-60 cm. Sojabonenzaad is gevoelig voor rot, zaadbehandeling kan worden gedaan voor het zaaien.";
"tools.calculator.land.advice.alfalfa" = "Luzernezaad is klein, zaaidiepte mag niet te diep zijn, ongeveer 1-2 cm. Bereid de grond zorgvuldig voor het zaaien en verdicht licht na het zaaien. Luzerne is gevoelig voor pH, zure bodems moeten eerst worden aangepast met kalk.";

// Land Management - Irrigatie advies
"tools.calculator.land.advice.water" = "Irrigatiewaterhoeveelheid moet worden aangepast volgens gewasgroeifase, klimaatomstandigheden en bodemtype. Aanbevolen om waterbesparende irrigatietechnologieën zoals druppelirrigatie of micro-sprinklerirrigatie te gebruiken. Beste irrigatietijden zijn vroege ochtend of avond om waterverdamping te verminderen. Vermijd over-irrigatie om wortelzuurstoftekort en bodemnutriëntenverlies te voorkomen.";

// Production Tracker
"tools.calculator.production.title" = "Productietracker";
"tools.calculator.production.description" = "Volg dierlijke melk- of eiproductie";
"tools.calculator.production.animalType" = "Diertype";
"tools.calculator.production.animalCount" = "Aantal dieren";
"tools.calculator.production.productionAmount" = "Productiehoeveelheid";
"tools.calculator.production.date" = "Datum";
"tools.calculator.production.addRecord" = "Record toevoegen";
"tools.calculator.production.records" = "Productierecords";
"tools.calculator.production.averagePerAnimal" = "Gemiddeld per dier";
"tools.calculator.production.totalProduction" = "Totale productie";
"tools.calculator.production.expectedRange" = "Verwacht bereik";
"tools.calculator.production.cow" = "Melkkoe";
"tools.calculator.production.goat" = "Melkgeit";
"tools.calculator.production.sheep" = "Melkschaap";
"tools.calculator.production.chicken" = "Leghen";
"tools.calculator.production.milk" = "Melk";
"tools.calculator.production.egg" = "Ei";
"tools.calculator.production.liter" = "Liter";
"tools.calculator.production.piece" = "Stuk";
"tools.calculator.production.noRecords" = "Nog geen productierecords";
"tools.calculator.production.recentRecords" = "Recente records";
"tools.calculator.production.stats" = "Productiestatistieken";
"tools.calculator.production.trend" = "Productietrend";
"tools.calculator.production.advice" = "Productiebeheer tips";
"tools.calculator.production.statisticalPeriod" = "Statistische periode (dagen)";
"tools.calculator.production.days" = "Dagen";
"tools.calculator.production.perDay" = "/dag";
"tools.calculator.production.compareWithExpected" = "Vergelijk met verwachte productie";
"tools.calculator.production.expected" = "Verwacht";
"tools.calculator.production.inputTotalProduction" = "Voer totale productie in";
"tools.calculator.production.production" = "Productie";

// Production Trends
"tools.calculator.production.trend.rising" = "Stijgend";
"tools.calculator.production.trend.falling" = "Dalend";
"tools.calculator.production.trend.stable" = "Stabiel";
"tools.calculator.production.trend.noData" = "Geen gegevens";
"tools.calculator.production.trend.insufficientData" = "Onvoldoende gegevens";

// Time Periods
"tools.calculator.production.period.7days" = "7 dagen";
"tools.calculator.production.period.14days" = "14 dagen";
"tools.calculator.production.period.30days" = "30 dagen";
"tools.calculator.production.period.90days" = "90 dagen";

// Production Advice
"tools.calculator.production.advice.cow.low" = "Huidige melkproductie ligt onder verwachting. Aanbevolen wordt de voerkwaliteit en voedingsverhouding te controleren, zorgen voor voldoende hoogwaardig ruwvoer en krachtvoer. Let op de stalenvironment, verminder stress en verbeter comfort. Controleer regelmatig de gezondheid van het vee en voorkom ziekten zoals mastitis.";
"tools.calculator.production.advice.cow.high" = "Uitstekende melkproductie prestatie! Blijf de huidige voedingsmanagement methoden handhaven. Vergeet niet de gezondheid van melkkoeien te monitoren en voorkom voedingsuitputting door overmatige melkproductie. Zorg ervoor dat voer voldoende energie en eiwit bevat.";
"tools.calculator.production.advice.cow.normal" = "Melkproductie ligt binnen normaal bereik. Handhaaf gebalanceerd voedingsmanagement, inclusief juiste voerverhoudingen, voldoende watervoorziening en comfortabele omgeving. Controleer regelmatig de kudde gezondheid en houd melkapparatuur schoon.";
"tools.calculator.production.advice.goat.low" = "Huidige melkproductie ligt onder verwachting. Aanbevolen wordt de voedingsomstandigheden te verbeteren, kwaliteitsvoer en schoon water te verstrekken. Controleer de gezondheid van dieren, vooral uiergezondheid. Pas melkfrequentie en -techniek aan, houd melkomgeving rustig en comfortabel.";
"tools.calculator.production.advice.goat.high" = "Uitstekende melkproductie prestatie! Blijf de huidige voedingsmanagement methoden handhaven. Monitor de lichaamsconditie van dieren om ervoor te zorgen dat hoge productie geen snelle gewichtsverlies veroorzaakt. Supplement voldoende voedingsstoffen.";
"tools.calculator.production.advice.goat.normal" = "Melkproductie ligt binnen normaal bereik. Handhaaf goed voedingsmanagement, inclusief juiste voerverhoudingen en comfortabele omgeving. Controleer regelmatig de gezondheid en houd melkapparatuur schoon en correct in gebruik.";
"tools.calculator.production.advice.sheep.low" = "Huidige melkproductie ligt onder verwachting. Aanbevolen wordt de voedingsomstandigheden te verbeteren, kwaliteitsvoer en schoon water te verstrekken. Controleer de gezondheid van dieren, vooral uiergezondheid. Pas melkfrequentie en -techniek aan, houd melkomgeving rustig en comfortabel.";
"tools.calculator.production.advice.sheep.high" = "Uitstekende melkproductie prestatie! Blijf de huidige voedingsmanagement methoden handhaven. Monitor de lichaamsconditie van dieren om ervoor te zorgen dat hoge productie geen snelle gewichtsverlies veroorzaakt. Supplement voldoende voedingsstoffen.";
"tools.calculator.production.advice.sheep.normal" = "Melkproductie ligt binnen normaal bereik. Handhaaf goed voedingsmanagement, inclusief juiste voerverhoudingen en comfortabele omgeving. Controleer regelmatig de gezondheid en houd melkapparatuur schoon en correct in gebruik.";
"tools.calculator.production.advice.chicken.low" = "Huidige eiproductie ligt onder verwachting. Aanbevolen wordt de voerkwaliteit te controleren om ervoor te zorgen dat leghennen voldoende eiwit, calcium en andere essentiële voedingsstoffen krijgen. Controleer verlichtingstijd (meestal 14-16 uur licht is ideaal). Controleer hokkenvironment en verminder stressfactoren.";
"tools.calculator.production.advice.chicken.high" = "Uitstekende eiproductie prestatie! Blijf de huidige voedingsmanagement methoden handhaven. Zorg voor voldoende calciumrijk voer om continue eierschaalvorming te ondersteunen. Vervang regelmatig strooisel en houd hok droog en schoon.";
"tools.calculator.production.advice.chicken.normal" = "Eiproductie ligt binnen normaal bereik. Handhaaf goed voedingsmanagement, inclusief gebalanceerd voer, voldoende watervoorziening en geschikte omgevingstemperatuur. Houd hok rustig, verminder verstoring en stress. Verzamel regelmatig eieren om te voorkomen dat hennen gaan broeden.";

// Feeding Advice Text
"tools.calculator.advice.cattle.young" = "Jonge runderen hebben eiwitrijk voer nodig. Verstrek kwaliteitskalvervoer. Voer 3-4 keer per dag en zorg voor voldoende vers water.";
"tools.calculator.advice.cattle.growing" = "Groeiende runderen hebben uitgebalanceerde voeding nodig met voldoende eiwit en energie. Voer kwaliteitshooi, ruwvoer en passend krachtvoer.";
"tools.calculator.advice.cattle.adult" = "Volwassen runderen kunnen voornamelijk ruwvoer eten, aangevuld met krachtvoer op basis van productiestatus. Zorg voor vers voer en voldoende water.";
"tools.calculator.advice.sheep" = "Schapen hebben kwaliteitsruwvoer nodig, vooral goed hooi. Bij beweiding, zorg voor voldoende weideoppervlakte. Supplement met mineralen, vooral koper en selenium.";
"tools.calculator.advice.pig.young" = "Biggen hebben hoogwaardig startvoer nodig, rijk aan eiwit en essentiële aminozuren. Voer kleine hoeveelheden frequent.";
"tools.calculator.advice.pig.adult" = "Groei-afmestvarkens hebben uitgebalanceerde rantsoenen nodig inclusief granen, eiwitaanvullingen en mineralen. Controleer voerhoeveelheden om obesitas te voorkomen.";
"tools.calculator.advice.chicken" = "Kippen hebben compleet voer nodig met passende niveaus van eiwit, energie, vitaminen en mineralen. Leghennen hebben extra calcium nodig.";
"tools.calculator.advice.duck" = "Eendenvoer moet voldoende eiwit en energie bevatten. Indien mogelijk, verstrek wateromgeving en wat groenvoer.";
"tools.calculator.advice.horse" = "Paarden eten voornamelijk kwaliteitshooi aangevuld met passende granen. Voer frequent in kleine hoeveelheden. Verstrek zoutblokken en mineralensupplementen.";
"tools.calculator.advice.default" = "Verstrek uitgebalanceerde voeding op basis van diersoort en groeifase, zorg voor voldoende watervoorziening.";