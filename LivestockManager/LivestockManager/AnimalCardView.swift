import SwiftUI

struct AnimalCardView: View {
    let livestock: Animal
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 14) {
                // 动物图标 - 改为扁平黑白风格
                AnimalIconView(species: livestock.species)
                
                // 牲畜信息
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(livestock.visualId)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                        
                        Spacer()
                        
                        StatusBadge(status: livestock.status)
                    }
                    
                    Text("\(L.Animal.species): \(livestock.species.displayName) | \(L.Animal.breed): \(livestock.breed ?? L.unknown)")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.black.opacity(0.6))
                        .lineLimit(1)
                    
                    Text("\(L.Animal.gender): \(livestock.sex.displayName) | \(L.Animal.age): \(livestock.age)")
                        .font(.system(size: 13, weight: .regular))
                        .foregroundColor(Color.black.opacity(0.6))
                        .lineLimit(1)
                }
                
                // 箭头图标
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.black.opacity(0.3))
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 动物图标视图
struct AnimalIconView: View {
    let species: Species
    
    var body: some View {
        ZStack {
            // 背景圆形 - 黑白风格
            Circle()
                .fill(Color.black.opacity(0.06))
                .frame(width: 60, height: 60)
            
            // 使用自定义动物图标而非系统图标
            Image(iconForSpecies(species))
                .resizable()
                .scaledToFit()
                .frame(width: 32, height: 32)
                .foregroundColor(.black)
        }
    }
    
    // 为每种动物选择对应的自定义图标
    private func iconForSpecies(_ species: Species) -> String {
        switch species {
        case .cattle:
            return "cow"
        case .sheep:
            return "sheep"
        case .pig:
            return "pig"
        case .horse:
            return "horse"
        case .chicken:
            return "orpington-chicken"
        case .duck:
            return "duck"
        case .goose:
            return "goose"
        case .other:
            return "rabbit" // 使用兔子图标作为其他动物的默认图标
        }
    }
}

struct StatusBadge: View {
    let status: AnimalStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(statusColor)
                .frame(width: 6, height: 6)
            
            Text(status.rawValue)
                .font(.system(size: 11, weight: .medium))
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color.black.opacity(0.05))
        .foregroundColor(Color.black.opacity(0.7))
        .cornerRadius(10)
    }
    
    private var statusColor: Color {
        switch status {
        case .active:
            return Color.black
        case .forSale:
            return Color.black.opacity(0.6)
        case .sold:
            return Color.black.opacity(0.4)
        case .deceased:
            return Color.black.opacity(0.3)
        case .other:
            return Color.black.opacity(0.5)
        }
    }
}

#Preview {
    VStack(spacing: 12) {
        AnimalCardView(
            livestock: Animal(
                visualId: "Cattle001",
                species: .cattle,
                breed: "Angus",
                sex: .male,
                birthDate: Calendar.current.date(byAdding: .year, value: -2, to: Date())!,
                source: .selfBred,
                status: .active
            ),
            onTap: {}
        )
        
        AnimalCardView(
            livestock: Animal(
                visualId: "Sheep001",
                species: .sheep,
                breed: "Merino",
                sex: .female,
                birthDate: Calendar.current.date(byAdding: .year, value: -1, to: Date())!,
                source: .selfBred,
                status: .forSale
            ),
            onTap: {}
        )
        
        AnimalCardView(
            livestock: Animal(
                visualId: "Horse001",
                species: .horse,
                breed: "Arabian",
                sex: .female,
                birthDate: Calendar.current.date(byAdding: .year, value: -3, to: Date())!,
                source: .purchased,
                status: .active
            ),
            onTap: {}
        )
        
        AnimalCardView(
            livestock: Animal(
                visualId: "Chicken001",
                species: .chicken,
                breed: "Leghorn",
                sex: .female,
                birthDate: Calendar.current.date(byAdding: .month, value: -8, to: Date())!,
                source: .selfBred,
                status: .forSale
            ),
            onTap: {}
        )
    }
    .padding()
    .background(Color(hex: "#F8F8F8"))
} 