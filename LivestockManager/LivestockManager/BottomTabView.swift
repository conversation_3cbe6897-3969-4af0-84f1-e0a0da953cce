import SwiftUI

struct BottomTabView: View {
    @Binding var selectedTab: Int
    let onAddTapped: () -> Void
    let onProfileTapped: () -> Void
    let onCameraTapped: () -> Void = {} // 默认空实现
    
    var body: some View {
        ZStack {
            // 底部背景 - 使用纯白色和细微阴影
            Rectangle()
                .fill(Color.white)
                .frame(height: 75)
                .shadow(color: Color.black.opacity(0.04), radius: 1, x: 0, y: -1)
            
            // 顶部分隔线 - 添加一条细线增强扁平感
            Rectangle()
                .fill(Color.black.opacity(0.08))
                .frame(height: 1)
                .offset(y: -37)
            
            // 底部主菜单
            HStack(spacing: 0) {
                // 主页标签
                TabButtonNew(
                    systemIcon: "house",
                    title: L.tabHome,
                    isSelected: selectedTab == 0,
                    action: { selectedTab = 0 }
                )
                
                // 代办标签
                TabButtonNew(
                    systemIcon: "checklist",
                    title: <PERSON>.tabTodo,
                    isSelected: selectedTab == 1,
                    action: { selectedTab = 1 }
                )
                
                // 中间留空给添加按钮
                Spacer()
                    .frame(width: 80)
                
                // 工具标签（原搜索标签）
                TabButtonNew(
                    systemIcon: "wrench.and.screwdriver",
                    title: L.tabTools,
                    isSelected: selectedTab == 3,
                    action: { selectedTab = 3 }
                )
                
                // 设置标签
                TabButtonNew(
                    systemIcon: "gearshape",
                    title: L.tabProfile,
                    isSelected: selectedTab == 2,
                    action: { 
                        selectedTab = 2
                        onProfileTapped()
                    }
                )
            }
            .padding(.horizontal, 16)
            
            // 中间添加按钮 - 简化为黑白风格
            Button(action: onAddTapped) {
                ZStack {
                    Circle()
                        .fill(Color.black)
                        .frame(width: 56, height: 56)
                        .shadow(color: Color.black.opacity(0.12), radius: 3, x: 0, y: 1)
                    
                    Image(systemName: "plus")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                }
            }
            .offset(y: -20)
        }
    }
}

struct TabButtonNew: View {
    let systemIcon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 5) {
                // 修复选中状态图标问题，检查是否有 .fill 版本
                if isSelected {
                    // 处理特殊图标，有些图标没有.fill版本
                    if systemIcon == "checklist" {
                        Image(systemName: "checklist")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                    } else if systemIcon == "magnifyingglass" {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                    } else if systemIcon == "wrench.and.screwdriver" {
                        Image(systemName: "wrench.and.screwdriver")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                    } else {
                        Image(systemName: "\(systemIcon).fill")
                            .font(.system(size: 20))
                            .foregroundColor(.black)
                    }
                } else {
                    Image(systemName: systemIcon)
                        .font(.system(size: 20))
                        .foregroundColor(Color.black.opacity(0.4))
                }
                
                Text(title)
                    .font(.system(size: 11, weight: isSelected ? .medium : .regular))
                    .foregroundColor(isSelected ? .black : Color.black.opacity(0.4))
            }
            .frame(maxWidth: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    VStack {
        Spacer()
        BottomTabView(
            selectedTab: .constant(0),
            onAddTapped: {},
            onProfileTapped: {}
        )
    }
    .background(Color.gray.opacity(0.1))
}