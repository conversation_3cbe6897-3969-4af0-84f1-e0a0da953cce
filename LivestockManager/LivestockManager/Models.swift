import Foundation

// App 总体数据结构
struct AppData: Codable {
    var schemaVersion: String = "1.0.0"
    var appSettings: AppSettings = AppSettings()
    var animals: [Animal] = []

    // 未来可以扩展其他数据，如 groups, customFieldsDefinitions 等
}

// App 设置
struct AppSettings: Codable {
    // 示例：最后同步时间戳，iCloud同步通常由系统处理，但App内记录有时有帮助
    var lastSyncTimestamp: Date? = nil
    // 未来可以存放App级别的设置，如默认物种、单位偏好等
    // var defaultSpecies: Species?
    // var preferredUnit: Unit?
}

// 动物数据结构
struct Animal: Codable, Identifiable {
    var id: UUID = UUID()                     // App内生成的UUID，用于内部管理
    var visualId: String                      // 用户可见的唯一标识 (耳标号/自定义编号) - V1.0搜索关键字段
    var photoPath: String? = nil              // 本地存储的图片路径 (相对于App的某个目录) - 可选
    var species: Species                      // V1.0筛选字段
    var breed: String? = nil                  // V1.0搜索可能涉及的字段
    var sex: Sex                              // V1.0筛选字段
    var birthDate: Date? = nil
    var source: AnimalSource                  // (预定义值: "Self-bred", "Purchased")
    var purchaseDetails: PurchaseDetails? = nil // 仅当 source 为 "Purchased" 时存在或有值
    var status: AnimalStatus                  // V1.0筛选字段
    var notes: String? = nil                  // 备注信息
    var createdAt: Date = Date()              // 记录创建时间
    var updatedAt: Date = Date()              // 记录最后更新时间

    // 构造函数，用于创建新动物或修复现有动物
    init(
        id: UUID = UUID(),
        visualId: String,
        photoPath: String? = nil,
        species: Species = .other,
        breed: String? = nil,
        sex: Sex = .male,
        birthDate: Date? = nil,
        source: AnimalSource = .selfBred,
        purchaseDetails: PurchaseDetails? = nil,
        status: AnimalStatus = .active,
        notes: String? = nil,
        createdAt: Date = Date(),
        updatedAt: Date = Date()
    ) {
        self.id = id
        self.visualId = visualId
        self.photoPath = photoPath
        self.species = species
        self.breed = breed
        self.sex = sex
        self.birthDate = birthDate
        self.source = source
        self.purchaseDetails = purchaseDetails
        self.status = status
        self.notes = notes
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // --- 未来扩展占位 ---
    // var groupIds: [UUID]? = nil
    // var sireId: UUID? = nil
    // var damId: UUID? = nil
    // var healthRecords: [HealthRecord]? = nil
    // var breedingRecords: [BreedingRecord]? = nil
    // var weightRecords: [WeightRecord]? = nil

    // 计算年龄 (从旧的 Livestock 结构迁移过来)
    var age: String {
        guard let birthDate = birthDate else {
            return L.unknown
        }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: birthDate, to: Date())
        let years = components.year ?? 0
        let months = components.month ?? 0
        
        // 根据当前语言返回不同格式的年龄描述
        let isEnglish = Locale.current.language.languageCode?.identifier == "en"
        
        if years > 0 && months > 0 {
            return isEnglish ? "\(years) yr \(months) mo" : "\(years)岁\(months)个月"
        } else if years > 0 {
            return isEnglish ? "\(years) yr" : "\(years)岁"
        } else if months > 0 {
            return isEnglish ? "\(months) mo" : "\(months)个月"
        } else {
            return isEnglish ? "Less than 1 month" : "不足1个月"
        }
    }

    // Codable 的 CodingKeys，用于自定义JSON键名（如果需要）
    // 如果Swift属性名和JSON键名一致，则不需要显式定义
    // enum CodingKeys: String, CodingKey {
    //     case id, visualId, photoPath, species, breed, sex, birthDate, source, purchaseDetails, status, notes, createdAt, updatedAt
    // }
}

// 物种 (预定义值)
enum Species: String, Codable, CaseIterable, Identifiable {
    case cattle = "cattle"
    case sheep = "sheep"
    case pig = "pig"
    case horse = "horse"
    case chicken = "chicken"
    case duck = "duck"
    case goose = "goose"
    case other = "other"

    var id: String { self.rawValue }

    // 本地化的显示名称
    var displayName: String {
        switch self {
        case .cattle: return L.Species.cattle
        case .sheep: return L.Species.sheep
        case .pig: return L.Species.pig
        case .horse: return L.Species.horse
        case .chicken: return L.Species.chicken
        case .duck: return L.Species.duck
        case .goose: return L.Species.goose
        case .other: return L.Species.other
        }
    }

    var icon: String {
        switch self {
        case .cattle: return "🐄"
        case .sheep: return "🐑"
        case .pig: return "🐷"
        case .horse: return "🐎"
        case .chicken: return "🐔"
        case .duck: return "🦆"
        case .goose: return "🦢"
        case .other: return "❓"
        }
    }
}

// 性别 (预定义值)
enum Sex: String, Codable, CaseIterable, Identifiable {
    case male = "male"
    case female = "female"
    case castrated = "castrated"

    var id: String { self.rawValue }

    // 本地化的显示名称
    var displayName: String {
        switch self {
        case .male: return L.Gender.male
        case .female: return L.Gender.female
        case .castrated: return L.Gender.castrated
        }
    }
}

// 动物来源
enum AnimalSource: String, Codable, CaseIterable, Identifiable {
    case selfBred = "selfBred"
    case purchased = "purchased"

    var id: String { self.rawValue }

    // 本地化的显示名称
    var displayName: String {
        switch self {
        case .selfBred: return L.Source.selfBred
        case .purchased: return L.Source.purchased
        }
    }
}

// 购买详情
struct PurchaseDetails: Codable {
    var date: Date? = nil
    var price: Double? = nil
    var currency: String? = "CNY" // 可选，未来国际化考虑
}

// 动物状态 (预定义值)
enum AnimalStatus: String, Codable, CaseIterable, Identifiable {
    case active = "active"
    case forSale = "forSale"
    case sold = "sold"
    case deceased = "deceased"
    case other = "other"

    var id: String { self.rawValue }

    // 本地化的显示名称
    var displayName: String {
        switch self {
        case .active: return L.Status.active
        case .forSale: return L.Status.forSale
        case .sold: return L.Status.sold
        case .deceased: return L.Status.deceased
        case .other: return L.Status.other
        }
    }

    var color: String {
        switch self {
        case .active: return "green"
        case .forSale: return "blue"
        case .sold: return "gray"
        case .deceased: return "red"
        case .other: return "orange"
        }
    }
}

// --- 未来扩展占位 ---
// struct Group: Codable, Identifiable {
//     var id: UUID = UUID()
//     var name: String
//     var description: String? = nil
//     var createdAt: Date = Date()
//     var updatedAt: Date = Date()
// }

// struct CustomFieldDefinition: Codable, Identifiable {
//     var id: UUID = UUID()
//     var name: String
//     var type: CustomFieldType
//     var appliesToSpecies: [Species]? = nil // 可选，如果该字段只适用于某些物种
// }

// enum CustomFieldType: String, Codable {
//     case text = "text"
//     case number = "number"
//     case date = "date"
//     // ... 其他类型
// }

// struct HealthRecord: Codable, Identifiable { /* ... */ }
// struct BreedingRecord: Codable, Identifiable { /* ... */ }
// struct WeightRecord: Codable, Identifiable { /* ... */ }

// 注意：旧的 LivestockStatistics 结构体已被移除。
// 如果需要统计功能，通常从 AppData.animals 动态计算。
// 统计数据模型 (从旧的 LivestockStatistics 迁移过来)
struct AnimalStatistics {
    let inStock: Int    // 存栏 (对应 .active)
    let forSale: Int    // 待售 (对应 .forSale)
    let sold: Int       // 已售 (对应 .sold)
    let dead: Int       // 死亡 (对应 .deceased)
}
