import SwiftUI
import Combine

// MARK: - 点击关闭键盘扩展
extension View {
    func dismissKeyboardOnTap() -> some View {
        self.onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }
}

struct AddAnimalView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var dataManager: AnimalDataManager
    
    // 编辑模式属性
    @State private var isEditMode: Bool = false
    @State private var editingAnimal: Animal? = nil
    
    @State private var livestockCode = ""
    @State private var selectedSpecies: Species?
    @State private var breed = ""
    @State private var selectedSex: Sex?
    @State private var birthDate = Date()
    @State private var selectedSource: AnimalSource = .selfBred
    @State private var purchaseDate = Date()
    @State private var purchasePrice = ""
    @State private var selectedStatus: AnimalStatus = .active
    @State private var notes = ""
    @State private var selectedImage: UIImage?
    @State private var showingImagePicker = false
    @State private var showingDatePicker = false
    @State private var showingPurchaseDatePicker = false
    
    // 验证状态
    @State private var isFormValid = false
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    
    // 验证提示状态
    @State private var showValidationErrors = false
    @State private var codeValid = false
    @State private var speciesValid = false
    @State private var sexValid = false
    
    // 默认初始化器 - 添加模式
    init() {
        self._isEditMode = State(initialValue: false)
        self._editingAnimal = State(initialValue: nil)
        self._selectedSpecies = State(initialValue: .cattle) // 默认选择牛
    }
    
    // 编辑模式初始化器
    init(animal: Animal) {
        self._isEditMode = State(initialValue: true)
        self._editingAnimal = State(initialValue: animal)
        
        // 预填充表单
        self._livestockCode = State(initialValue: animal.visualId)
        self._selectedSpecies = State(initialValue: animal.species)
        self._breed = State(initialValue: animal.breed ?? "")
        self._selectedSex = State(initialValue: animal.sex)
        
        if let birthDate = animal.birthDate {
            self._birthDate = State(initialValue: birthDate)
        }
        
        self._selectedSource = State(initialValue: animal.source)
        
        if let purchaseDetails = animal.purchaseDetails {
            if let purchaseDate = purchaseDetails.date {
                self._purchaseDate = State(initialValue: purchaseDate)
            }
            if let price = purchaseDetails.price {
                self._purchasePrice = State(initialValue: String(price))
            }
        }
        
        self._selectedStatus = State(initialValue: animal.status)
        self._notes = State(initialValue: animal.notes ?? "")
        
        // 初始不设置图片，而是在onAppear中加载
        // 这是因为这里无法访问dataManager
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                Color(hex: "#F8F8F8")
                    .ignoresSafeArea()
                    .dismissKeyboardOnTap() // 点击背景关闭键盘
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 牲畜照片上传区域
                        PhotoUploadSection(
                            selectedImage: $selectedImage,
                            showingImagePicker: $showingImagePicker,
                            selectedSpecies: $selectedSpecies
                        )
                        
                        // 表单字段
                        VStack(spacing: 20) {
                            // 唯一标识
                            FormField(
                                title: L.Animal.id,
                                isRequired: true,
                                content: {
                                    TextField(L.Add.idHint, text: $livestockCode)
                                        .textFieldStyle(CustomTextFieldStyle(
                                            isValid: !showValidationErrors || codeValid
                                        ))
                                }
                            )
                            
                            // 一行放两个：物种和性别
                            HStack(spacing: 12) {
                                // 物种
                                FormField(
                                    title: L.Animal.species,
                                    isRequired: true,
                                    content: {
                                        SpeciesPickerView(selectedSpecies: $selectedSpecies)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(showValidationErrors && !speciesValid ? Color.red : Color.black.opacity(0), lineWidth: 1)
                                            )
                                    }
                                )
                                
                                // 性别
                                FormField(
                                    title: L.Animal.gender,
                                    isRequired: true,
                                    content: {
                                        SexPickerView(selectedSex: $selectedSex)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(showValidationErrors && !sexValid ? Color.red : Color.black.opacity(0), lineWidth: 1)
                                            )
                                    }
                                )
                            }
                            
                            // 品种
                            FormField(
                                title: L.Animal.breed,
                                content: {
                                    TextField(L.Add.breedPlaceholder, text: $breed)
                                        .textFieldStyle(CustomTextFieldStyle())
                                }
                            )
                            
                            // 出生日期单独一行
                            FormField(
                                title: L.Animal.birthDate,
                                isRequired: true,
                                content: {
                                    DatePickerField(
                                        date: $birthDate,
                                        showingPicker: $showingDatePicker,
                                        placeholder: Locale.current.language.languageCode?.identifier == "en" ? "yyyy/mm/dd" : "年/月/日"
                                    )
                                }
                            )
                            
                            // 一行放两个：状态和来源
                            HStack(spacing: 12) {
                                // 状态
                                FormField(
                                    title: L.Animal.status,
                                    isRequired: true,
                                    content: {
                                        StatusPickerView(selectedStatus: $selectedStatus)
                                    }
                                )
                                
                                // 来源
                                FormField(
                                    title: L.Animal.source,
                                    isRequired: true,
                                    content: {
                                        SourcePickerView(selectedSource: $selectedSource)
                                    }
                                )
                            }
                            
                            // 购入信息（条件显示）
                            if selectedSource == .purchased {
                                // 一行放两个：购入日期和价格
                                HStack(spacing: 12) {
                                    FormField(
                                        title: L.Animal.purchaseDate,
                                        content: {
                                            DatePickerField(
                                                date: $purchaseDate,
                                                showingPicker: $showingPurchaseDatePicker,
                                                placeholder: Locale.current.language.languageCode?.identifier == "en" ? "yyyy/mm/dd" : "年/月/日"
                                            )
                                        }
                                    )
                                    
                                    FormField(
                                        title: L.Animal.purchasePrice,
                                        content: {
                                            TextField(L.Add.pricePlaceholder, text: $purchasePrice)
                                                .textFieldStyle(CustomTextFieldStyle())
                                                .keyboardType(.decimalPad)
                                        }
                                    )
                                }
                            }
                            
                            // 备注
                            FormField(
                                title: L.Animal.notes,
                                content: {
                                    ZStack(alignment: .topLeading) {
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.white)
                                            .frame(minHeight: 80)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                                            )
                                        
                                        if notes.isEmpty {
                                            Text(L.Add.notesPlaceholder)
                                                .foregroundColor(Color.black.opacity(0.4))
                                                .padding(.horizontal, 16)
                                                .padding(.vertical, 12)
                                        }
                                        
                                        TextEditor(text: $notes)
                                            .padding(.horizontal, 12)
                                            .padding(.vertical, 8)
                                            .background(Color.clear)
                                    }
                                }
                            )
                        }
                        .padding(.horizontal, 16)
                        
                        // 必填项提示
                        HStack {
                            Text(L.Add.requiredFields)
                                .font(.caption)
                                .foregroundColor(Color.black.opacity(0.6))
                            Spacer()
                        }
                        .padding(.horizontal, 16)
                        
                        // 底部安全区域
                        Color.clear.frame(height: 100)
                    }
                    .padding(.top, 16)
                }
                .dismissKeyboardOnTap() // 点击滚动区域关闭键盘
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(L.cancel) {
                        dismiss()
                    }
                    .foregroundColor(.black)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(isEditMode ? L.update : L.save) {
                        validateForm()
                        if isFormValid {
                            saveAnimal()
                        } else {
                            showValidationErrors = true
                            showingValidationAlert = true
                            validationMessage = L.Error.required
                        }
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.black)
                    .cornerRadius(20)
                }
                
                // 添加键盘工具栏以方便关闭键盘
                ToolbarItemGroup(placement: .keyboard) {
                    Spacer()
                    Button(L.done) {
                        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                    }
                    .foregroundColor(.black)
                }
            }
            .navigationTitle(isEditMode ? L.Add.editTitle : L.Add.title)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .onAppear {
                // 如果是编辑模式，加载现有图片
                if isEditMode, let animal = editingAnimal, let photoPath = animal.photoPath {
                    selectedImage = dataManager.loadImage(fileName: photoPath)
                }
            }
        }
        .alert(L.Error.title, isPresented: $showingValidationAlert) {
            Button(L.confirm, role: .cancel) {}
        } message: {
            Text(validationMessage)
        }
    }
    
    // 验证表单
    private func validateForm() {
        // 验证必填字段
        codeValid = !livestockCode.isEmpty
        speciesValid = selectedSpecies != nil
        sexValid = selectedSex != nil
        
        // 更新验证状态
        isFormValid = codeValid && speciesValid && sexValid
    }
    
    func saveAnimal() {
        // 验证必填项
        guard !livestockCode.isEmpty, let species = selectedSpecies, let sex = selectedSex else {
            showingValidationAlert = true
            validationMessage = L.Error.required
            return
        }
        
        // 创建购买详情（如果是购入的动物）
        var purchaseDetails: PurchaseDetails? = nil
        if selectedSource == .purchased {
            var price: Double? = nil
            if !purchasePrice.isEmpty {
                price = Double(purchasePrice)
            }
            purchaseDetails = PurchaseDetails(date: purchaseDate, price: price)
        }
        
        // 处理图片存储 (实际应用中需要实现图片存储逻辑)
        var photoPath: String? = nil
        if let selectedImage = selectedImage {
            // 检查图片尺寸是否有效
            if selectedImage.size.width <= 0 || selectedImage.size.height <= 0 || 
               !selectedImage.size.width.isFinite || !selectedImage.size.height.isFinite {
                print("❌ 保存动物时发现图片尺寸无效: \(selectedImage.size)")
            } else {
                // 保存图片到文件系统，并获取文件名
                photoPath = dataManager.saveImage(selectedImage)
            }
        } else if isEditMode, let animal = editingAnimal {
            // 如果是编辑模式且没有选择新图片，保留原有图片路径
            photoPath = animal.photoPath
        }
        
        // 创建或更新动物对象
        if isEditMode, let animal = editingAnimal {
            // 编辑模式 - 更新现有动物
            var updatedAnimal = animal
            updatedAnimal.visualId = livestockCode
            updatedAnimal.photoPath = photoPath
            updatedAnimal.species = species
            updatedAnimal.breed = breed.isEmpty ? nil : breed
            updatedAnimal.sex = sex
            updatedAnimal.birthDate = birthDate
            updatedAnimal.source = selectedSource
            updatedAnimal.purchaseDetails = purchaseDetails
            updatedAnimal.status = selectedStatus
            updatedAnimal.notes = notes.isEmpty ? nil : notes
            updatedAnimal.updatedAt = Date()
            
            // 更新到数据管理器
            dataManager.updateAnimal(updatedAnimal)
        } else {
            // 添加模式 - 创建新动物
            let animal = Animal(
                visualId: livestockCode,
                photoPath: photoPath,
                species: species,
                breed: breed.isEmpty ? nil : breed,
                sex: sex,
                birthDate: birthDate,
                source: selectedSource,
                purchaseDetails: purchaseDetails,
                status: selectedStatus,
                notes: notes.isEmpty ? nil : notes
            )
            
            // 添加到数据管理器
            dataManager.addAnimal(animal)
        }
        
        // 返回上一页
        dismiss()
    }
}

// MARK: - 照片上传组件
struct PhotoUploadSection: View {
    @Binding var selectedImage: UIImage?
    @Binding var showingImagePicker: Bool
    @Binding var selectedSpecies: Species?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(L.Animal.photo)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.black)
                .padding(.horizontal, 16)
            
            Button(action: { showingImagePicker = true }) {
                if let selectedImage = selectedImage {
                    // 有选择的图片
                    VStack {
                        ZStack {
                            // 图片
                            Image(uiImage: selectedImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 120, height: 120)
                                .clipShape(RoundedRectangle(cornerRadius: 12))
                            
                            // 编辑图标容器 - 使用VStack而不是background
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Image(systemName: "pencil.circle.fill")
                                        .font(.system(size: 28))
                                        .foregroundColor(.white)
                                        .padding(8)
                                }
                            }
                            .frame(width: 120, height: 120)
                        }
                        .frame(width: 120, height: 120)
                    }
                } else if let species = selectedSpecies {
                    // 没有选择图片但选择了物种，显示默认图标
                    VStack {
                        ZStack {
                            // 背景圆形
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.black.opacity(0.04))
                                .frame(width: 120, height: 120)
                            
                            // 动物图标
                            Image(iconForSpecies(species))
                                .resizable()
                                .scaledToFit()
                                .frame(width: 50, height: 50)
                                .foregroundColor(Color.black.opacity(0.5))
                        }
                        .overlay(
                            VStack {
                                Spacer()
                                HStack {
                                    Spacer()
                                    Image(systemName: "plus.circle.fill")
                                        .font(.system(size: 28))
                                        .foregroundColor(.black.opacity(0.5))
                                        .padding(8)
                                }
                            }
                            .frame(width: 120, height: 120)
                        )
                    }
                } else {
                    // 无选择的图片也无选择物种
                    VStack {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.black.opacity(0.04))
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack(spacing: 8) {
                                    Image(systemName: "camera")
                                        .font(.system(size: 24))
                                        .foregroundColor(Color.black.opacity(0.5))
                                    
                                    Text(L.Animal.addPhoto)
                                        .font(.system(size: 14))
                                        .foregroundColor(Color.black.opacity(0.5))
                                }
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), style: StrokeStyle(lineWidth: 1, dash: [5]))
                            )
                    }
                }
            }
            .padding(.horizontal, 16)
            .sheet(isPresented: $showingImagePicker) {
                ImagePicker(selectedImage: $selectedImage)
            }
        }
    }
    
    // 为每种动物选择对应的自定义图标
    private func iconForSpecies(_ species: Species) -> String {
        switch species {
        case .cattle:
            return "cow" // 牛图标
        case .sheep:
            return "sheep" // 羊图标
        case .pig:
            return "pig" // 猪图标
        case .horse:
            return "horse" // 马图标
        case .chicken:
            return "orpington-chicken" // 鸡图标
        case .duck:
            return "duck" // 鸭图标
        case .goose:
            return "goose" // 鹅图标
        case .other:
            return "rabbit" // 其他动物图标（使用兔子作为默认）
        }
    }
}

// MARK: - 表单字段组件
struct FormField<Content: View>: View {
    let title: String
    let isRequired: Bool
    let content: () -> Content
    let hint: String?
    
    init(title: String, isRequired: Bool = false, @ViewBuilder content: @escaping () -> Content, hint: String? = nil) {
        self.title = title
        self.isRequired = isRequired
        self.content = content
        self.hint = hint
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                if isRequired {
                    Text("*")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.red)
                }
            }
            
            content()
            
            if let hint = hint {
                Text(hint)
                    .font(.caption)
                    .foregroundColor(Color.black.opacity(0.5))
            }
        }
    }
}

// MARK: - 自定义文本框样式
struct CustomTextFieldStyle: TextFieldStyle {
    let minHeight: CGFloat
    let isValid: Bool
    
    init(minHeight: CGFloat = 44, isValid: Bool = true) {
        self.minHeight = minHeight
        self.isValid = isValid
    }
    
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: minHeight)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isValid ? Color.black.opacity(0.08) : Color.red, lineWidth: 1)
            )
    }
}

// MARK: - 日期选择器字段
struct DatePickerField: View {
    @Binding var date: Date
    @Binding var showingPicker: Bool
    let placeholder: String
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        // 根据当前语言设置日期格式
        let isEnglish = Locale.current.language.languageCode?.identifier == "en"
        formatter.dateFormat = isEnglish ? "yyyy/MM/dd" : "yyyy年MM月dd日"
        return formatter
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Button(action: { showingPicker.toggle() }) {
                HStack {
                    Text(dateFormatter.string(from: date))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Image(systemName: "calendar")
                        .foregroundColor(Color.black.opacity(0.4))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(minHeight: 44)
                .background(Color.white)
                .cornerRadius(showingPicker ? 12 : 12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.black.opacity(0.08), lineWidth: 1)
                )
            }
            
            if showingPicker {
                DatePicker("", selection: $date, displayedComponents: .date)
                    .datePickerStyle(.compact)
                    .labelsHidden()
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color.white)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.black.opacity(0.08), lineWidth: 1)
                    )
            }
        }
    }
}

// MARK: - 物种选择器
struct SpeciesPickerView: View {
    @Binding var selectedSpecies: Species?
    @State private var isExpanded = false
    
    var body: some View {
        Menu {
            ForEach(Species.allCases, id: \.self) { species in
                Button(action: {
                    selectedSpecies = species
                }) {
                    HStack {
                        Text(species.displayName)
                        if selectedSpecies == species {
                            Spacer()
                            Image(systemName: "checkmark")
                                .foregroundColor(.black)
                        }
                    }
                }
            }
        } label: {
            HStack {
                Text(selectedSpecies?.displayName ?? L.Animal.species)
                    .foregroundColor(selectedSpecies == nil ? Color.black.opacity(0.4) : .black)
                
                Spacer()
                
                Image(systemName: "chevron.down")
                    .foregroundColor(Color.black.opacity(0.4))
                    .font(.system(size: 14))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: 44)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
    }
}

// MARK: - 性别选择器
struct SexPickerView: View {
    @Binding var selectedSex: Sex?
    
    var body: some View {
        Menu {
            ForEach(Sex.allCases, id: \.self) { sex in
                Button(action: {
                    selectedSex = sex
                }) {
                    HStack {
                        Text(sex.displayName)
                        if selectedSex == sex {
                            Spacer()
                            Image(systemName: "checkmark")
                                .foregroundColor(.black)
                        }
                    }
                }
            }
        } label: {
            HStack {
                Text(selectedSex?.displayName ?? L.Animal.gender)
                    .foregroundColor(selectedSex == nil ? Color.black.opacity(0.4) : .black)
                
                Spacer()
                
                Image(systemName: "chevron.down")
                    .foregroundColor(Color.black.opacity(0.4))
                    .font(.system(size: 14))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: 44)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
    }
}

// MARK: - 来源选择器
struct SourcePickerView: View {
    @Binding var selectedSource: AnimalSource
    
    var body: some View {
        Menu {
            ForEach(AnimalSource.allCases, id: \.self) { source in
                Button(action: {
                    selectedSource = source
                }) {
                    HStack {
                        Text(source.displayName)
                        if selectedSource == source {
                            Spacer()
                            Image(systemName: "checkmark")
                                .foregroundColor(.black)
                        }
                    }
                }
            }
        } label: {
            HStack {
                Text(selectedSource.displayName)
                    .foregroundColor(.black)
                
                Spacer()
                
                Image(systemName: "chevron.down")
                    .foregroundColor(Color.black.opacity(0.4))
                    .font(.system(size: 14))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: 44)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
    }
}

// MARK: - 状态选择器
struct StatusPickerView: View {
    @Binding var selectedStatus: AnimalStatus
    
    var body: some View {
        Menu {
            ForEach(AnimalStatus.allCases, id: \.self) { status in
                Button(action: {
                    selectedStatus = status
                }) {
                    HStack {
                        Text(status.displayName)
                        if selectedStatus == status {
                            Spacer()
                            Image(systemName: "checkmark")
                                .foregroundColor(.black)
                        }
                    }
                }
            }
        } label: {
            HStack {
                Text(selectedStatus.displayName)
                    .foregroundColor(.black)
                
                Spacer()
                
                Image(systemName: "chevron.down")
                    .foregroundColor(Color.black.opacity(0.4))
                    .font(.system(size: 14))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .frame(minHeight: 44)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = true
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            var selectedUIImage: UIImage? = nil
            
            if let editedImage = info[.editedImage] as? UIImage {
                selectedUIImage = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                selectedUIImage = originalImage
            }
            
            // 检查图片是否有效
            if let image = selectedUIImage {
                print("选择的图片尺寸: \(image.size.width) x \(image.size.height)")
                
                // 检查图片尺寸是否有效
                if image.size.width <= 0 || image.size.height <= 0 || 
                   !image.size.width.isFinite || !image.size.height.isFinite {
                    print("警告：选择了无效尺寸的图片")
                    // 不设置图片
                } else {
                    // 图片尺寸有效，设置到binding
                    parent.selectedImage = image
                }
            }
            
            parent.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

#Preview {
    AddAnimalView()
        .environmentObject(AnimalDataManager())
} 