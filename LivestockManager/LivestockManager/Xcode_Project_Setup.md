# Xcode项目配置指南

在Xcode中编译该项目前，需要进行以下配置调整：

## 修复Localizable.xcstrings冲突问题

如果遇到错误：`Localizable.xcstrings cannot co-exist with other .strings or .stringsdict tables with the same name.`，请按照以下步骤操作：

1. 在Xcode中打开项目
2. 在项目导航器中，查找并右键点击`Localizable.xcstrings`文件（如果存在）
3. 选择"Delete"并确认"Remove Reference"（只移除引用而不删除实际文件）
4. 然后执行清理操作：选择菜单 Product -> Clean Build Folder (⇧⌘K)
5. 关闭并重新打开Xcode项目

## 配置本地化设置

在项目设置中进行如下本地化配置：

1. 点击项目文件，选择主目标(Target)
2. 切换到"Info"标签页
3. 在"Localizations"部分，确保项目包含以下语言：
   - English (Base language)
   - Chinese (Simplified)
4. 添加中文支持时，选择需要本地化的资源文件（特别是Localizable.strings文件）

## 确保本地化资源被正确引用

1. 验证项目中包含以下本地化资源文件夹：
   - `Resources/en.lproj/Localizable.strings`
   - `Resources/zh-Hans.lproj/Localizable.strings`

2. 确保这些文件已添加到项目的"Copy Bundle Resources"构建阶段中

## 清理构建缓存

如果遇到奇怪的编译错误，尝试以下步骤：

1. 选择菜单 Product -> Clean Build Folder (⇧⌘K)
2. 删除项目的derived data:
   - 菜单 Xcode -> Preferences -> Locations
   - 点击Derived Data旁边的箭头图标，打开Finder
   - 删除与此项目相关的文件夹
3. 重启Xcode 