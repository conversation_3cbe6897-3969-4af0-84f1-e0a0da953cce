import SwiftUI

// 成本分析视图
struct CostAnalyzerView: View {
    @State private var selectedAnimalType: String = "cattle"
    @State private var numberOfAnimals: Int = 10
    @State private var raisingPeriod: Int = 12 // 月
    
    // 成本输入
    @State private var feedCostPerMonth: Double = 200
    @State private var vetCostPerMonth: Double = 50
    @State private var otherCostPerMonth: Double = 30
    @State private var laborCostPerMonth: Double = 100
    
    // 收入输入
    @State private var expectedSalePrice: Double = 1500
    
    // 计算结果
    @State private var totalCost: Double = 0
    @State private var totalRevenue: Double = 0
    @State private var profit: Double = 0
    @State private var roi: Double = 0
    @State private var costBreakdown: [CostItem] = []
    
    let animalTypes = [
        ("cattle", L.Tools.Calculator.Animal.cattle, "🐄"),
        ("sheep", L.Tools.Calculator.Animal.sheep, "🐑"),
        ("pig", L.Tools.Calculator.Animal.pig, "🐷"),
        ("chicken", L.Tools.Calculator.Animal.chicken, "🐔"),
        ("duck", L.Tools.Calculator.Animal.duck, "🦆"),
        ("horse", L.Tools.Calculator.Animal.horse, "🐎")
    ]
    
    struct CostItem: Identifiable {
        let id = UUID()
        let name: String
        let value: Double
        let color: Color
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.Calculator.costTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)

                Text(L.Tools.Calculator.costDescription)
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 参数输入卡片
            VStack(spacing: 20) {
                // 动物类型选择
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalType)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(animalTypes, id: \.0) { type in
                                AnimalTypeButton(
                                    icon: type.2,
                                    name: type.1,
                                    isSelected: selectedAnimalType == type.0,
                                    action: {
                                        selectedAnimalType = type.0
                                        updateDefaultValues()
                                        calculateCosts()
                                    }
                                )
                            }
                        }
                    }
                }
                
                // 动物数量
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalCount)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)

                    HStack {
                        TextField(L.Tools.Calculator.animalCount, value: $numberOfAnimals, formatter: NumberFormatter())
                            .keyboardType(.numberPad)
                            .padding()
                            .background(Color.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                            )
                            .onChange(of: numberOfAnimals) { _ in
                                calculateCosts()
                            }
                    }
                }

                // 饲养周期
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.raisingPeriod)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)

                    HStack {
                        Slider(value: $raisingPeriod.doubleValue, in: 1...36, step: 1)
                            .onChange(of: raisingPeriod) { _ in
                                calculateCosts()
                            }

                        Text("\(raisingPeriod) " + L.Tools.Calculator.months)
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .frame(width: 60)
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 成本输入卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.monthlyCostEstimate)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                // 饲料成本
                HStack {
                    Text(L.Tools.Calculator.feedCost)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.7))
                        .frame(width: 80, alignment: .leading)

                    TextField(L.Tools.Calculator.monthlyFeedCost, value: $feedCostPerMonth, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: feedCostPerMonth) { _ in
                            calculateCosts()
                        }
                }
                
                // 兽医成本
                HStack {
                    Text(L.Tools.Calculator.vetCost)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.7))
                        .frame(width: 80, alignment: .leading)

                    TextField(L.Tools.Calculator.monthlyVetCost, value: $vetCostPerMonth, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: vetCostPerMonth) { _ in
                            calculateCosts()
                        }
                }
                
                // 劳动力成本
                HStack {
                    Text(L.Tools.Calculator.laborShort)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.7))
                        .frame(width: 80, alignment: .leading)

                    TextField(L.Tools.Calculator.monthlyLaborCost, value: $laborCostPerMonth, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: laborCostPerMonth) { _ in
                            calculateCosts()
                        }
                }
                
                // 其他成本
                HStack {
                    Text(L.Tools.Calculator.otherCost)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.7))
                        .frame(width: 80, alignment: .leading)

                    TextField(L.Tools.Calculator.monthlyOtherCost, value: $otherCostPerMonth, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: otherCostPerMonth) { _ in
                            calculateCosts()
                        }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 收入输入卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.expectedRevenue)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 预期销售价格
                HStack {
                    Text(L.Tools.Calculator.salePrice)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.7))
                        .frame(width: 80, alignment: .leading)

                    TextField(L.Tools.Calculator.perAnimalSalePrice, value: $expectedSalePrice, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: expectedSalePrice) { _ in
                            calculateCosts()
                        }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 分析结果卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.analysisResults)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack(spacing: 20) {
                    // 总成本
                    VStack {
                        Text(String(format: "¥%.2f", totalCost))
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.black)
                        
                        Text(L.Tools.Calculator.totalCost)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.6))
                    }
                    .frame(maxWidth: .infinity)

                    // 总收入
                    VStack {
                        Text(String(format: "¥%.2f", totalRevenue))
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.black)

                        Text(L.Tools.Calculator.totalRevenue)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.6))
                    }
                    .frame(maxWidth: .infinity)

                    // 利润
                    VStack {
                        Text(String(format: "¥%.2f", profit))
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(profit >= 0 ? .green : .red)

                        Text(L.Tools.Calculator.profit)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.6))
                    }
                    .frame(maxWidth: .infinity)
                }
                
                // 投资回报率
                VStack(spacing: 8) {
                    Text(L.Tools.Calculator.roiLabel)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.6))
                    
                    Text(String(format: "%.1f%%", roi))
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(roi >= 0 ? .green : .red)
                }
                .padding(.top, 8)
                
                // 成本明细
                VStack(alignment: .leading, spacing: 12) {
                    Text(L.Tools.Calculator.costBreakdown)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))
                    
                    ForEach(costBreakdown) { item in
                        HStack {
                            Circle()
                                .fill(item.color)
                                .frame(width: 12, height: 12)
                            
                            Text(item.name)
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text(String(format: "¥%.2f", item.value))
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                            
                            Text(String(format: "(%.1f%%)", item.value / totalCost * 100))
                                .font(.system(size: 12))
                                .foregroundColor(Color.black.opacity(0.5))
                        }
                    }
                }
                .padding(.top, 16)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            updateDefaultValues()
            calculateCosts()
        }
    }
    
    // 根据动物类型更新默认值
    private func updateDefaultValues() {
        switch selectedAnimalType {
        case "cattle":
            feedCostPerMonth = 200
            vetCostPerMonth = 50
            otherCostPerMonth = 30
            laborCostPerMonth = 100
            expectedSalePrice = 8000
        case "sheep":
            feedCostPerMonth = 60
            vetCostPerMonth = 20
            otherCostPerMonth = 15
            laborCostPerMonth = 50
            expectedSalePrice = 1200
        case "pig":
            feedCostPerMonth = 120
            vetCostPerMonth = 30
            otherCostPerMonth = 20
            laborCostPerMonth = 70
            expectedSalePrice = 1500
        case "chicken":
            feedCostPerMonth = 8
            vetCostPerMonth = 2
            otherCostPerMonth = 3
            laborCostPerMonth = 10
            expectedSalePrice = 50
        case "duck":
            feedCostPerMonth = 10
            vetCostPerMonth = 2
            otherCostPerMonth = 3
            laborCostPerMonth = 12
            expectedSalePrice = 60
        case "horse":
            feedCostPerMonth = 300
            vetCostPerMonth = 100
            otherCostPerMonth = 60
            laborCostPerMonth = 150
            expectedSalePrice = 15000
        default:
            break
        }
    }
    
    // 计算成本和收益
    private func calculateCosts() {
        // 计算每个成本项
        let totalFeedCost = feedCostPerMonth * Double(numberOfAnimals) * Double(raisingPeriod)
        let totalVetCost = vetCostPerMonth * Double(numberOfAnimals) * Double(raisingPeriod)
        let totalLaborCost = laborCostPerMonth * Double(raisingPeriod)
        let totalOtherCost = otherCostPerMonth * Double(numberOfAnimals) * Double(raisingPeriod)
        
        // 总成本
        totalCost = totalFeedCost + totalVetCost + totalLaborCost + totalOtherCost
        
        // 总收入
        totalRevenue = expectedSalePrice * Double(numberOfAnimals)
        
        // 利润
        profit = totalRevenue - totalCost
        
        // 投资回报率
        roi = totalCost > 0 ? (profit / totalCost * 100) : 0
        
        // 成本明细
        costBreakdown = [
            CostItem(name: L.Tools.Calculator.feedCost, value: totalFeedCost, color: Color.blue),
            CostItem(name: L.Tools.Calculator.vetCost, value: totalVetCost, color: Color.green),
            CostItem(name: L.Tools.Calculator.laborCost, value: totalLaborCost, color: Color.orange),
            CostItem(name: L.Tools.Calculator.otherCost, value: totalOtherCost, color: Color.purple)
        ]
    }
} 