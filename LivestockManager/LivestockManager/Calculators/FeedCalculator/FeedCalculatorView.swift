import SwiftUI

// 饲料计算器视图
struct FeedCalculatorView: View {
    @State private var selectedAnimalType: String = "cattle"
    @State private var animalAge: Double = 12
    @State private var animalWeight: Double = 500
    @State private var numberOfAnimals: Int = 1
    @State private var feedRequirement: Double = 0
    @State private var feedType: String = "grain"
    
    let animalTypes = [
        ("cattle", L.Tools.Calculator.Animal.cattle, "🐄"),
        ("sheep", L.Tools.Calculator.Animal.sheep, "🐑"),
        ("pig", L.Tools.Calculator.Animal.pig, "🐷"),
        ("chicken", L.Tools.Calculator.Animal.chicken, "🐔"),
        ("duck", L.Tools.Calculator.Animal.duck, "🦆"),
        ("horse", L.Tools.Calculator.Animal.horse, "🐎")
    ]

    let feedTypes = [
        ("grain", L.Tools.Calculator.Feed.grain),
        ("hay", L.Tools.Calculator.Feed.hay),
        ("silage", <PERSON><PERSON><PERSON>.Calculator.Feed.silage),
        ("concentrate", L.Tools.Calculator.Feed.concentrate)
    ]
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.Calculator.feedTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)

                Text(L.Tools.Calculator.feedDescription)
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 参数输入卡片
            VStack(spacing: 20) {
                // 动物类型选择
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalType)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(animalTypes, id: \.0) { type in
                                AnimalTypeButton(
                                    icon: type.2,
                                    name: type.1,
                                    isSelected: selectedAnimalType == type.0,
                                    action: {
                                        selectedAnimalType = type.0
                                        calculateFeedRequirement()
                                    }
                                )
                            }
                        }
                    }
                }
                
                // 动物年龄
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalAge)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)

                    HStack {
                        Slider(value: $animalAge, in: 1...60, step: 1)
                            .onChange(of: animalAge) { _ in
                                calculateFeedRequirement()
                            }

                        Text("\(Int(animalAge))" + L.Tools.Calculator.months)
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .frame(width: 60)
                    }
                }

                // 动物体重
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalWeight)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)

                    HStack {
                        Slider(value: $animalWeight, in: 1...1000, step: 1)
                            .onChange(of: animalWeight) { _ in
                                calculateFeedRequirement()
                            }

                        Text("\(Int(animalWeight))" + L.Tools.Calculator.kg)
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .frame(width: 80)
                    }
                }

                // 动物数量
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalCount)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    HStack {
                        Button(action: { 
                            if numberOfAnimals > 1 {
                                numberOfAnimals -= 1
                                calculateFeedRequirement()
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black.opacity(0.8))
                        }
                        
                        Text("\(numberOfAnimals)")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                            .frame(width: 40)
                        
                        Button(action: { 
                            numberOfAnimals += 1
                            calculateFeedRequirement()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black)
                        }
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 饲料类型
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.Calculator.feedType)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)

                Picker(L.Tools.Calculator.feedType, selection: $feedType) {
                    ForEach(feedTypes, id: \.0) { type in
                        Text(type.1).tag(type.0)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: feedType) { _ in
                    calculateFeedRequirement()
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 计算结果卡片
            VStack(spacing: 16) {
                Text(L.Tools.Calculator.feedRequirement)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.black)

                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        // 单个动物
                        HStack {
                            Text(L.Tools.Calculator.singleAnimal)
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))

                            Spacer()

                            Text(String(format: "%.2f ", feedRequirement) + L.Tools.Calculator.kg)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }

                        // 全部动物
                        if numberOfAnimals > 1 {
                            HStack {
                                Text(L.Tools.Calculator.allAnimals)
                                    .font(.system(size: 16))
                                    .foregroundColor(Color.black.opacity(0.7))

                                Spacer()

                                Text(String(format: "%.2f ", feedRequirement * Double(numberOfAnimals)) + L.Tools.Calculator.kg)
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.black)
                            }
                        }

                        // 每周
                        HStack {
                            Text(L.Tools.Calculator.weekly)
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))

                            Spacer()

                            Text(String(format: "%.2f ", feedRequirement * Double(numberOfAnimals) * 7) + L.Tools.Calculator.kg)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }

                        // 每月
                        HStack {
                            Text(L.Tools.Calculator.monthly)
                                .font(.system(size: 16))
                                .foregroundColor(Color.black.opacity(0.7))

                            Spacer()

                            Text(String(format: "%.2f ", feedRequirement * Double(numberOfAnimals) * 30) + L.Tools.Calculator.kg)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                        }
                    }
                }
                .padding(.top, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 饲料建议卡片
            VStack(alignment: .leading, spacing: 12) {
                Text(L.Tools.Calculator.feedingAdvice)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)

                Text(getFeedingAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            calculateFeedRequirement()
        }
    }
    
    // 计算饲料需求
    private func calculateFeedRequirement() {
        // 基于动物类型、年龄和体重计算每日饲料需求
        // 这里使用简化的计算公式，实际应用中可以使用更复杂的营养学公式
        
        var baseRequirement: Double = 0
        
        switch selectedAnimalType {
        case "cattle":
            // 牛的饲料需求：体重的2-3%
            let ageFactorMultiplier = animalAge < 12 ? 0.03 : (animalAge < 24 ? 0.025 : 0.02)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "sheep":
            // 羊的饲料需求：体重的2-4%
            let ageFactorMultiplier = animalAge < 6 ? 0.04 : (animalAge < 12 ? 0.03 : 0.02)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "pig":
            // 猪的饲料需求：体重的3-5%
            let ageFactorMultiplier = animalAge < 3 ? 0.05 : (animalAge < 6 ? 0.04 : 0.03)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "chicken":
            // 鸡的饲料需求（以克计）：体重的5-8%
            let ageFactorMultiplier = animalAge < 1 ? 0.08 : (animalAge < 3 ? 0.07 : 0.05)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "duck":
            // 鸭的饲料需求：体重的5-8%
            let ageFactorMultiplier = animalAge < 1 ? 0.08 : (animalAge < 3 ? 0.07 : 0.05)
            baseRequirement = animalWeight * ageFactorMultiplier
        case "horse":
            // 马的饲料需求：体重的1.5-2.5%
            let ageFactorMultiplier = animalAge < 12 ? 0.025 : (animalAge < 36 ? 0.02 : 0.015)
            baseRequirement = animalWeight * ageFactorMultiplier
        default:
            baseRequirement = animalWeight * 0.025
        }
        
        // 根据饲料类型调整
        switch feedType {
        case "grain":
            feedRequirement = baseRequirement * 0.4  // 谷物通常是总需求的30-50%
        case "hay":
            feedRequirement = baseRequirement * 0.8  // 干草通常是反刍动物主要饲料
        case "silage":
            feedRequirement = baseRequirement * 1.2  // 青贮饲料水分含量高，需要更多重量
        case "concentrate":
            feedRequirement = baseRequirement * 0.3  // 浓缩饲料营养密度高，需要较少量
        default:
            feedRequirement = baseRequirement
        }
    }
    
    // 获取饲养建议
    private func getFeedingAdvice() -> String {
        switch selectedAnimalType {
        case "cattle":
            if animalAge < 6 {
                return L.Tools.Calculator.Advice.cattleYoung
            } else if animalAge < 12 {
                return L.Tools.Calculator.Advice.cattleGrowing
            } else {
                return L.Tools.Calculator.Advice.cattleAdult
            }
        case "sheep":
            return L.Tools.Calculator.Advice.sheep
        case "pig":
            if animalAge < 3 {
                return L.Tools.Calculator.Advice.pigYoung
            } else {
                return L.Tools.Calculator.Advice.pigAdult
            }
        case "chicken":
            return L.Tools.Calculator.Advice.chicken
        case "duck":
            return L.Tools.Calculator.Advice.duck
        case "horse":
            return L.Tools.Calculator.Advice.horse
        default:
            return L.Tools.Calculator.Advice.defaultAdvice
        }
    }
} 