import SwiftUI

// 生产追踪视图
struct ProductionTrackerView: View {
    @State private var selectedAnimalType: ProductionAnimalType = .cow
    @State private var animalCount: Int = 10
    @State private var productionDays: Int = 30
    @State private var totalProduction: Double = 0
    @State private var productionHistory: [ProductionRecord] = []
    @State private var averagePerAnimal: Double = 0
    @State private var productionTrend: String = "稳定"
    @State private var showingAddRecord = false
    @State private var newProductionDate = Date()
    @State private var newProductionAmount: Double = 0
    
    enum ProductionAnimalType: String, CaseIterable, Identifiable {
        case cow = "cow"
        case goat = "goat"
        case sheep = "sheep"
        case chicken = "chicken"
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .cow: return L.Tools.ProductionTracker.cow
            case .goat: return L.Tools.ProductionTracker.goat
            case .sheep: return L.Tools.ProductionTracker.sheep
            case .chicken: return L.Tools.ProductionTracker.chicken
            }
        }

        var productName: String {
            switch self {
            case .cow, .goat, .sheep: return L.Tools.ProductionTracker.milk
            case .chicken: return L.Tools.ProductionTracker.egg
            }
        }

        var productUnit: String {
            switch self {
            case .cow, .goat, .sheep: return L.Tools.ProductionTracker.liter
            case .chicken: return L.Tools.ProductionTracker.piece
            }
        }
        
        var icon: String {
            switch self {
            case .cow: return "🐄"
            case .goat: return "🐐"
            case .sheep: return "🐑"
            case .chicken: return "🐔"
            }
        }
        
        var expectedProduction: ClosedRange<Double> {
            switch self {
            case .cow: return 15...40 // 每头牛每天15-40升牛奶
            case .goat: return 2...5 // 每只山羊每天2-5升羊奶
            case .sheep: return 1...3 // 每只绵羊每天1-3升羊奶
            case .chicken: return 0.7...1.0 // 每只鸡每天0.7-1.0枚鸡蛋
            }
        }
    }
    
    struct ProductionRecord: Identifiable {
        let id = UUID()
        let date: Date
        let amount: Double
        let animalType: ProductionAnimalType
        let animalCount: Int
        
        var averagePerAnimal: Double {
            return animalCount > 0 ? amount / Double(animalCount) : 0
        }
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.ProductionTracker.title)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)

                Text(L.Tools.ProductionTracker.description)
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 动物类型选择卡片
            VStack(spacing: 20) {
                Text(L.Tools.ProductionTracker.animalType)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack(spacing: 10) {
                    ForEach(ProductionAnimalType.allCases) { type in
                        Button(action: {
                            selectedAnimalType = type
                            generateSampleData()
                            calculateProduction()
                        }) {
                            VStack(spacing: 8) {
                                Text(type.icon)
                                    .font(.system(size: 24))
                                
                                Text(type.displayName)
                                    .font(.system(size: 14, weight: selectedAnimalType == type ? .semibold : .regular))
                                    .foregroundColor(selectedAnimalType == type ? .black : Color.black.opacity(0.6))
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .background(selectedAnimalType == type ? Color.black.opacity(0.05) : Color.clear)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(selectedAnimalType == type ? Color.black.opacity(0.1) : Color.clear, lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                
                // 动物数量
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.ProductionTracker.animalCount)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))
                    
                    HStack {
                        Button(action: { 
                            if animalCount > 1 {
                                animalCount -= 1
                                calculateProduction()
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black.opacity(0.8))
                        }
                        
                        Text("\(animalCount)")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                            .frame(width: 40, alignment: .center)
                        
                        Button(action: { 
                            animalCount += 1
                            calculateProduction()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(Color.black)
                        }
                    }
                }
                
                // 统计周期
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.ProductionTracker.statisticalPeriod)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))

                    Picker(L.Tools.ProductionTracker.statisticalPeriod, selection: $productionDays) {
                        Text(L.Tools.ProductionTracker.days7).tag(7)
                        Text(L.Tools.ProductionTracker.days14).tag(14)
                        Text(L.Tools.ProductionTracker.days30).tag(30)
                        Text(L.Tools.ProductionTracker.days90).tag(90)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .onChange(of: productionDays) { _ in
                        generateSampleData()
                        calculateProduction()
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 生产统计卡片
            VStack(spacing: 20) {
                HStack {
                    Text(L.Tools.ProductionTracker.productionStats)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)

                    Spacer()

                    Button(action: { showingAddRecord = true }) {
                        Label(L.Tools.ProductionTracker.addRecord, systemImage: "plus")
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }
                
                VStack(spacing: 16) {
                    // 总产量
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(L.Tools.ProductionTracker.totalProduction)
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))

                            Text("\(String(format: "%.1f", totalProduction)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.black)
                        }

                        Spacer()

                        VStack(alignment: .trailing, spacing: 4) {
                            Text(L.Tools.ProductionTracker.averagePerAnimal)
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))

                            Text("\(String(format: "%.1f", averagePerAnimal)) \(selectedAnimalType.productUnit)\(L.Tools.ProductionTracker.perDay)")
                                .font(.system(size: 20, weight: .bold))
                                .foregroundColor(.black)
                        }
                    }
                    
                    // 趋势指示器
                    HStack {
                        Text("\(L.Tools.ProductionTracker.productionTrend):")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))

                        Text(productionTrend)
                            .font(.system(size: 14, weight: .semibold))
                            .foregroundColor(getTrendColor())

                        Image(systemName: getTrendIcon())
                            .foregroundColor(getTrendColor())
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 预期产量对比
                    VStack(alignment: .leading, spacing: 8) {
                        Text(L.Tools.ProductionTracker.compareWithExpected)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))

                        HStack {
                            Text("\(L.Tools.ProductionTracker.averagePerAnimal)\(L.Tools.ProductionTracker.perDay): \(String(format: "%.1f", averagePerAnimal)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 14))
                                .foregroundColor(.black)

                            Spacer()

                            Text("\(L.Tools.ProductionTracker.expected): \(String(format: "%.1f", selectedAnimalType.expectedProduction.lowerBound))-\(String(format: "%.1f", selectedAnimalType.expectedProduction.upperBound)) \(selectedAnimalType.productUnit)")
                                .font(.system(size: 14))
                                .foregroundColor(isWithinExpectedRange() ? .green : .orange)
                        }
                    }
                }
                .padding(.vertical, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 历史记录卡片
            VStack(alignment: .leading, spacing: 12) {
                Text(L.Tools.ProductionTracker.recentRecords)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)

                if productionHistory.isEmpty {
                    Text(L.Tools.ProductionTracker.noRecords)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.5))
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 20)
                } else {
                    ForEach(Array(productionHistory.prefix(5).enumerated()), id: \.element.id) { index, record in
                        HStack {
                            Text(formatDate(record.date))
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Spacer()
                            
                            Text("\(String(format: "%.1f", record.amount)) \(record.animalType.productUnit)")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.black)
                            
                            Text("(\(String(format: "%.1f", record.averagePerAnimal)) \(record.animalType.productUnit)/只)")
                                .font(.system(size: 12))
                                .foregroundColor(Color.black.opacity(0.5))
                        }
                        .padding(.vertical, 8)
                        
                        if index < min(4, productionHistory.count - 1) {
                            Divider()
                                .background(Color.black.opacity(0.1))
                        }
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 生产管理提示卡片
            VStack(alignment: .leading, spacing: 12) {
                Text(L.Tools.ProductionTracker.productionAdvice)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)

                Text(getProductionAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .sheet(isPresented: $showingAddRecord) {
            AddProductionRecordView(
                animalType: selectedAnimalType,
                animalCount: animalCount,
                onSave: { date, amount in
                    let newRecord = ProductionRecord(
                        date: date,
                        amount: amount,
                        animalType: selectedAnimalType,
                        animalCount: animalCount
                    )
                    productionHistory.insert(newRecord, at: 0)
                    calculateProduction()
                }
            )
        }
        .onAppear {
            generateSampleData()
            calculateProduction()
        }
    }
    
    // 生成示例数据
    private func generateSampleData() {
        // 清空现有数据
        productionHistory.removeAll()
        
        // 生成样本数据，用于演示
        let calendar = Calendar.current
        let today = Date()
        var dailyAverage: Double
        
        switch selectedAnimalType {
        case .cow:
            dailyAverage = 25.0 // 每天平均25升牛奶
        case .goat:
            dailyAverage = 3.5 // 每天平均3.5升羊奶
        case .sheep:
            dailyAverage = 2.0 // 每天平均2升羊奶
        case .chicken:
            dailyAverage = 0.85 // 每天平均0.85枚鸡蛋
        }
        
        // 生成过去 productionDays 天的记录
        for day in 0..<productionDays {
            if let recordDate = calendar.date(byAdding: .day, value: -day, to: today) {
                // 添加一些随机变化，使数据更真实
                let randomFactor = Double.random(in: 0.9...1.1)
                let amount = dailyAverage * Double(animalCount) * randomFactor
                
                // 创建记录
                let record = ProductionRecord(
                    date: recordDate,
                    amount: amount,
                    animalType: selectedAnimalType,
                    animalCount: animalCount
                )
                
                productionHistory.append(record)
            }
        }
        
        // 按日期排序
        productionHistory.sort { $0.date > $1.date }
    }
    
    // 计算生产统计数据
    private func calculateProduction() {
        guard !productionHistory.isEmpty else {
            totalProduction = 0
            averagePerAnimal = 0
            productionTrend = L.Tools.ProductionTracker.trendNoData
            return
        }
        
        // 计算总产量
        totalProduction = productionHistory.reduce(0) { $0 + $1.amount }
        
        // 计算每只动物的平均每日产量
        let totalDays = min(productionDays, productionHistory.count)
        if totalDays > 0 && animalCount > 0 { // Ensure no division by zero
            let avgPerDay = totalProduction / Double(totalDays)
            averagePerAnimal = avgPerDay / Double(animalCount)
        } else {
            averagePerAnimal = 0
        }
        
        // 分析趋势（比较前半段和后半段的平均值）
        if productionHistory.count >= 4 {
            let halfPoint = productionHistory.count / 2
            let firstHalf = productionHistory.suffix(from: halfPoint)
            let secondHalf = productionHistory.prefix(halfPoint)
            
            let firstHalfAvg = firstHalf.reduce(0) { $0 + $1.amount } / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.reduce(0) { $0 + $1.amount } / Double(secondHalf.count)
            
            if firstHalfAvg > 0 { // Ensure no division by zero
                let percentChange = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
                if percentChange > 5 {
                    productionTrend = L.Tools.ProductionTracker.trendRising
                } else if percentChange < -5 {
                    productionTrend = L.Tools.ProductionTracker.trendFalling
                } else {
                    productionTrend = L.Tools.ProductionTracker.trendStable
                }
            } else {
                 productionTrend = L.Tools.ProductionTracker.trendStable // If first half avg is 0, consider stable or insufficient data
            }
        } else {
            productionTrend = L.Tools.ProductionTracker.trendInsufficientData
        }
    }
    
    // 获取趋势图标
    private func getTrendIcon() -> String {
        switch productionTrend {
        case L.Tools.ProductionTracker.trendRising:
            return "arrow.up.circle.fill"
        case L.Tools.ProductionTracker.trendFalling:
            return "arrow.down.circle.fill"
        case L.Tools.ProductionTracker.trendStable:
            return "equal.circle.fill"
        default:
            return "questionmark.circle.fill"
        }
    }

    // 获取趋势颜色
    private func getTrendColor() -> Color {
        switch productionTrend {
        case L.Tools.ProductionTracker.trendRising:
            return .green
        case L.Tools.ProductionTracker.trendFalling:
            return .red
        case L.Tools.ProductionTracker.trendStable:
            return .blue
        default:
            return Color.black.opacity(0.5)
        }
    }
    
    // 检查是否在预期产量范围内
    private func isWithinExpectedRange() -> Bool {
        let range = selectedAnimalType.expectedProduction
        return range.contains(averagePerAnimal)
    }
    
    // 获取生产管理建议
    private func getProductionAdvice() -> String {
        switch selectedAnimalType {
        case .cow:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return L.Tools.ProductionTracker.Advice.Cow.low
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return L.Tools.ProductionTracker.Advice.Cow.high
            } else {
                return L.Tools.ProductionTracker.Advice.Cow.normal
            }
        case .goat:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return L.Tools.ProductionTracker.Advice.Goat.low
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return L.Tools.ProductionTracker.Advice.Goat.high
            } else {
                return L.Tools.ProductionTracker.Advice.Goat.normal
            }
        case .sheep:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return L.Tools.ProductionTracker.Advice.Sheep.low
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return L.Tools.ProductionTracker.Advice.Sheep.high
            } else {
                return L.Tools.ProductionTracker.Advice.Sheep.normal
            }
        case .chicken:
            if averagePerAnimal < selectedAnimalType.expectedProduction.lowerBound {
                return L.Tools.ProductionTracker.Advice.Chicken.low
            } else if averagePerAnimal > selectedAnimalType.expectedProduction.upperBound {
                return L.Tools.ProductionTracker.Advice.Chicken.high
            } else {
                return L.Tools.ProductionTracker.Advice.Chicken.normal
            }
        }
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd"
        return formatter.string(from: date)
    }
} 