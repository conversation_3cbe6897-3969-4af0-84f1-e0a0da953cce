import SwiftUI

// 添加生产记录视图
struct AddProductionRecordView: View {
    let animalType: ProductionTrackerView.ProductionAnimalType // Assuming ProductionTrackerView is available in this scope
    let animalCount: Int
    let onSave: (Date, Double) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedDate = Date()
    @State private var productionAmount: Double = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    But<PERSON>(L.cancel) {
                        dismiss()
                    }
                    .foregroundColor(.black)

                    Spacer()

                    Text("\(L.Tools.ProductionTracker.addRecord)\(animalType.productName)\(L.Tools.ProductionTracker.records)")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)

                    Spacer()

                    Button(L.save) {
                        onSave(selectedDate, productionAmount)
                        dismiss()
                    }
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(Color.white)
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 日期选择
                        VStack(alignment: .leading, spacing: 8) {
                            Text(L.Tools.ProductionTracker.date)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                            
                            DatePicker("", selection: $selectedDate, displayedComponents: .date)
                                .datePickerStyle(WheelDatePickerStyle())
                                .labelsHidden()
                                .frame(maxWidth: .infinity)
                        }
                        .padding(16)
                        .background(Color.white)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        
                        // 产量输入
                        VStack(alignment: .leading, spacing: 16) {
                            Text(L.Tools.ProductionTracker.production)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)

                            HStack {
                                TextField(L.Tools.ProductionTracker.inputTotalProduction, value: $productionAmount, formatter: NumberFormatter())
                                    .keyboardType(.decimalPad)
                                    .padding()
                                    .background(Color.white)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.black.opacity(0.08), lineWidth: 1)
                                    )

                                Text(animalType.productUnit)
                                    .font(.system(size: 16))
                                    .foregroundColor(Color.black.opacity(0.7))
                                    .frame(width: 40)
                            }

                            if productionAmount > 0 && animalCount > 0 {
                                HStack {
                                    Text("\(L.Tools.ProductionTracker.averagePerAnimal):")
                                        .font(.system(size: 14))
                                        .foregroundColor(Color.black.opacity(0.7))

                                    Spacer()

                                    Text(String(format: "%.2f %@", productionAmount / Double(animalCount), animalType.productUnit))
                                        .font(.system(size: 14, weight: .medium))
                                        .foregroundColor(.black)
                                }
                            }
                        }
                        .padding(16)
                        .background(Color.white)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                    }
                    .padding(16)
                }
                .background(Color(hex: "#F8F8F8")) // Assuming Color(hex:) is available
            }
            .navigationBarHidden(true)
        }
    }
}

// Preview might need adjustment if ProductionTrackerView.ProductionAnimalType is not directly accessible
// For now, commenting it out or providing a mock might be necessary if it causes build issues immediately.
/*
#Preview {
    AddProductionRecordView(animalType: .cow, animalCount: 10, onSave: {_,_  in })
}
*/ 