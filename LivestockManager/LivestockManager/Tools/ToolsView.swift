import SwiftUI
import StoreKit

struct ToolsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showingCounterTool = false
    @State private var showingConverterTool = false
    @State private var showingWeatherTool = false
    @State private var showingFarmCalculator = false
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            HStack {
                Text(L.Tools.title)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color.white)
            
            // 分割线
            Rectangle()
                .fill(Color.black.opacity(0.05))
                .frame(height: 1)
            
            // 工具网格
            ScrollView {
                VStack(spacing: 20) {
                    // 工具分类标题
                    HStack {
                        Text(L.Tools.basicTools)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color.black.opacity(0.8))
                        Spacer()
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 16)
                    
                    LazyVGrid(columns: columns, spacing: 16) {
                        // 计数器
                        ToolItemView(
                            icon: "number.circle",
                            title: L.<PERSON>ls.counter,
                            action: { showingCounterTool = true }
                        )
                        
                        // 农场计算器
                        ToolItemView(
                            icon: "function",
                            title: L.Tools.calculator,
                            action: { showingFarmCalculator = true }
                        )
                        
                        // 天气
                        ToolItemView(
                            icon: "cloud.sun",
                            title: L.Tools.weather,
                            action: { showingWeatherTool = true }
                        )
                        
                        // 单位转换
                        ToolItemView(
                            icon: "arrow.2.squarepath",
                            title: L.Tools.converter,
                            action: { showingConverterTool = true }
                        )
                    }
                    .padding(.horizontal, 24)
                    
                    // 分割线
                    Rectangle()
                        .fill(Color.black.opacity(0.05))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 8)
                    
                    // 用户建议区域
                    VStack(spacing: 12) {
                        HStack {
                            Text(L.Tools.suggestions)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(Color.black.opacity(0.8))
                            Spacer()
                        }
                        
                        Button(action: openAppStoreReview) {
                            HStack {
                                Image(systemName: "plus.circle")
                                    .font(.system(size: 20))
                                    .foregroundColor(.blue)
                                
                                Text(L.Tools.suggestNewTool)
                                    .font(.system(size: 15))
                                    .foregroundColor(.blue)
                                
                                Spacer()
                                
                                Image(systemName: "chevron.right")
                                    .font(.system(size: 14))
                                    .foregroundColor(Color.black.opacity(0.4))
                            }
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                            )
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 8)
                }
                .padding(.vertical, 10)
                
                // 底部安全区域
                Color.clear.frame(height: 100)
            }
            .background(Color(hex: "#F8F8F8"))
        }
        .sheet(isPresented: $showingCounterTool) {
            CounterToolView()
        }
        .sheet(isPresented: $showingConverterTool) {
            UnitConverterView()
        }
        .sheet(isPresented: $showingWeatherTool) {
            WeatherView()
        }
        .sheet(isPresented: $showingFarmCalculator) {
            FarmCalculatorView()
        }
    }
    
    private func openAppStoreReview() {
        // AppID: 6746741414
        let appStoreURL = URL(string: "https://apps.apple.com/app/id6746741414?action=write-review")
        if let url = appStoreURL, UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }
}

// 工具项视图
struct ToolItemView: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // 图标
                ZStack {
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.white)
                        .frame(width: 90, height: 90)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .shadow(color: Color.black.opacity(0.03), radius: 3, x: 0, y: 1)
                    
                    Image(systemName: icon)
                        .font(.system(size: 32))
                        .foregroundColor(.black)
                }
                
                // 标题
                Text(title)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.black)
                    .lineLimit(1)
                    .fixedSize(horizontal: false, vertical: true)
                    .multilineTextAlignment(.center)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    ToolsView()
} 

// Assuming L.Tools and Color(hex:) are defined elsewhere.
// For example:
// struct L {
//    struct Tools {
//        static let title = "实用工具"
//        static let counter = "计数器"
//        static let calculator = "农场计算器"
//        static let weather = "天气预报"
//        static let converter = "单位换算"
//        static let basicTools = "基础工具"
//        static let suggestions = "用户建议"
//        static let suggestNewTool = "建议新工具"
//    }
// }
// extension Color {
//    init(hex: String) { ... }
// } 