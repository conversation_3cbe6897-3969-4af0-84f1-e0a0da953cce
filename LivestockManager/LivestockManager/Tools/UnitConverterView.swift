import SwiftUI

struct UnitConverterView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedCategory = 0
    @State private var inputValue = ""
    @State private var fromUnitIndex = 0
    @State private var toUnitIndex = 1
    
    // 转换类别
    private let categories = [
        L.Tools.Converter.area,
        L.Tools.Converter.weight,
        L.Tools.Converter.volume,
        L.Tools.Converter.length,
        L.Tools.Converter.temperature
    ]
    
    // 单位定义
    private let units: [[UnitOption]] = [
        // 面积单位
        [
            UnitOption(name: L.Tools.Converter.Unit.squareMeter, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.mu, factor: 1/666.7),
            UnitOption(name: L.Tools.Converter.Unit.hectare, factor: 1/10000),
            UnitOption(name: L.Tools.Converter.Unit.squareKilometer, factor: 1/1000000),
            UnitOption(name: L.Tools.Converter.Unit.acre, factor: 1/4046.86)
        ],
        // 重量单位
        [
            UnitOption(name: <PERSON>.Tools.Converter.Unit.kilogram, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.gram, factor: 1000),
            UnitOption(name: L.Tools.Converter.Unit.jin, factor: 2),
            UnitOption(name: L.Tools.Converter.Unit.ton, factor: 0.001),
            UnitOption(name: L.Tools.Converter.Unit.pound, factor: 2.20462)
        ],
        // 容量单位
        [
            UnitOption(name: L.Tools.Converter.Unit.liter, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.milliliter, factor: 1000),
            UnitOption(name: L.Tools.Converter.Unit.cubicMeter, factor: 0.001),
            UnitOption(name: L.Tools.Converter.Unit.gallon, factor: 0.264172)
        ],
        // 长度单位
        [
            UnitOption(name: L.Tools.Converter.Unit.meter, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.centimeter, factor: 100),
            UnitOption(name: L.Tools.Converter.Unit.kilometer, factor: 0.001),
            UnitOption(name: L.Tools.Converter.Unit.foot, factor: 3.28084),
            UnitOption(name: L.Tools.Converter.Unit.inch, factor: 39.3701)
        ],
        // 温度单位 (特殊情况)
        [
            UnitOption(name: L.Tools.Converter.Unit.celsius, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.fahrenheit, factor: 1),
            UnitOption(name: L.Tools.Converter.Unit.kelvin, factor: 1)
        ]
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                    }
                    
                    Spacer()
                    
                    Text(L.Tools.converter)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    // 占位
                    Color.clear.frame(width: 16, height: 16)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(Color.white)
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 类别选择器
                        CategorySelectorView(
                            categories: categories,
                            selectedIndex: $selectedCategory
                        )
                        
                        // 输入区域
                        VStack(spacing: 20) {
                            // 输入值
                            VStack(alignment: .leading, spacing: 8) {
                                Text(L.Tools.Converter.value)
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.black)
                                
                                TextField(L.Tools.Converter.placeholder, text: $inputValue)
                                    .keyboardType(.decimalPad)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 14)
                                    .background(Color.white)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.black.opacity(0.08), lineWidth: 1)
                                    )
                            }
                            
                            // 单位选择
                            HStack(spacing: 20) {
                                // 从单位
                                VStack(alignment: .leading, spacing: 8) {
                                    Text(L.Tools.Converter.from)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.black)
                                    
                                    UnitPickerView(
                                        units: units[selectedCategory],
                                        selectedUnitIndex: $fromUnitIndex
                                    )
                                }
                                
                                // 转换箭头
                                VStack {
                                    Spacer()
                                    
                                    Button(action: { swapUnits() }) {
                                        Image(systemName: "arrow.left.arrow.right")
                                            .font(.system(size: 18))
                                            .foregroundColor(.black)
                                            .frame(width: 40, height: 40)
                                            .background(Color.black.opacity(0.04))
                                            .cornerRadius(20)
                                    }
                                    .padding(.top, 8)
                                }
                                
                                // 到单位
                                VStack(alignment: .leading, spacing: 8) {
                                    Text(L.Tools.Converter.to)
                                        .font(.system(size: 16, weight: .semibold))
                                        .foregroundColor(.black)
                                    
                                    UnitPickerView(
                                        units: units[selectedCategory],
                                        selectedUnitIndex: $toUnitIndex
                                    )
                                }
                            }
                        }
                        .padding(20)
                        .background(Color.white)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        
                        // 结果显示
                        VStack(spacing: 16) {
                            Text(L.Tools.Converter.result)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.black.opacity(0.7))
                            
                            Text(convertedResult)
                                .font(.system(size: 32, weight: .semibold))
                                .foregroundColor(.black)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 16)
                            
                            if !inputValue.isEmpty && Double(inputValue) != nil {
                                Text("\(inputValue) \(units[selectedCategory][fromUnitIndex].name) = \(convertedResult) \(units[selectedCategory][toUnitIndex].name)")
                                    .font(.system(size: 14))
                                    .foregroundColor(Color.black.opacity(0.6))
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, 16)
                            }
                        }
                        .padding(20)
                        .frame(maxWidth: .infinity)
                        .background(Color.white)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        
                        // 常用转换参考
                        VStack(alignment: .leading, spacing: 16) {
                            Text(L.Tools.Converter.reference)
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(.black)
                            
                            VStack(spacing: 10) {
                                ForEach(commonConversions(for: selectedCategory), id: \.self) { conversion in
                                    HStack {
                                        Text(conversion)
                                            .font(.system(size: 14))
                                            .foregroundColor(Color.black.opacity(0.7))
                                        Spacer()
                                    }
                                }
                            }
                            .padding(16)
                            .background(Color.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                            )
                        }
                        
                        // 底部安全区域
                        Color.clear.frame(height: 40)
                    }
                    .padding(16)
                }
                .background(Color(hex: "#F8F8F8"))
                .onTapGesture {
                    hideKeyboard()
                }
            }
            .navigationBarHidden(true)
            .onChange(of: selectedCategory) { newCategory in
                // 确保单位索引在有效范围内
                fromUnitIndex = 0
                toUnitIndex = min(1, units[newCategory].count - 1)
                inputValue = "" // 切换类别时清空输入
            }
        }
    }
    
    // 转换逻辑
    private var convertedResult: String {
        guard let value = Double(inputValue.replacingOccurrences(of: ",", with: ".")) else {
            return "0"
        }
        
        // 处理温度特殊情况
        if selectedCategory == 4 {
            return convertTemperature(value)
        }
        
        let fromUnit = units[selectedCategory][fromUnitIndex]
        let toUnit = units[selectedCategory][toUnitIndex]
        
        let valueInBaseUnit = value / fromUnit.factor
        let result = valueInBaseUnit * toUnit.factor
        
        return formatResult(result)
    }
    
    private func convertTemperature(_ value: Double) -> String {
        let from = units[4][fromUnitIndex].name
        let to = units[4][toUnitIndex].name
        var result: Double = value
        
        if from == L.Tools.Converter.Unit.celsius {
            if to == L.Tools.Converter.Unit.fahrenheit {
                result = (value * 9/5) + 32
            } else if to == L.Tools.Converter.Unit.kelvin {
                result = value + 273.15
            }
        } else if from == L.Tools.Converter.Unit.fahrenheit {
            if to == L.Tools.Converter.Unit.celsius {
                result = (value - 32) * 5/9
            } else if to == L.Tools.Converter.Unit.kelvin {
                result = (value - 32) * 5/9 + 273.15
            }
        } else if from == L.Tools.Converter.Unit.kelvin {
            if to == L.Tools.Converter.Unit.celsius {
                result = value - 273.15
            } else if to == L.Tools.Converter.Unit.fahrenheit {
                result = (value - 273.15) * 9/5 + 32
            }
        }
        return formatResult(result)
    }
    
    private func formatResult(_ number: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.maximumFractionDigits = 5
        formatter.minimumFractionDigits = 0
        return formatter.string(from: NSNumber(value: number)) ?? "0"
    }
    
    private func swapUnits() {
        let temp = fromUnitIndex
        fromUnitIndex = toUnitIndex
        toUnitIndex = temp
    }
    
    private func commonConversions(for categoryIndex: Int) -> [String] {
        switch categoryIndex {
        case 0: // 面积
            return [
                L.Tools.Converter.Reference.muToSqM,
                L.Tools.Converter.Reference.hectareToSqM,
                L.Tools.Converter.Reference.acreToSqM
            ]
        case 1: // 重量
            return [
                L.Tools.Converter.Reference.kgToJin,
                L.Tools.Converter.Reference.tonToKg,
                L.Tools.Converter.Reference.poundToKg
            ]
        case 2: // 容量
            return [
                L.Tools.Converter.Reference.literToMl,
                L.Tools.Converter.Reference.gallonToLiter
            ]
        case 3: // 长度
            return [
                L.Tools.Converter.Reference.kmToM,
                L.Tools.Converter.Reference.footToM,
                L.Tools.Converter.Reference.inchToCm
            ]
        case 4: // 温度
            return [
                L.Tools.Converter.Reference.celsiusToFahrenheit,
                L.Tools.Converter.Reference.fahrenheitToCelsius,
                L.Tools.Converter.Reference.celsiusToKelvin
            ]
        default: return []
        }
    }
}

// 单位选项模型
struct UnitOption: Hashable {
    let name: String
    let factor: Double // 相对于基本单位的换算因子
}

// MARK: - Subviews
struct CategorySelectorView: View {
    let categories: [String]
    @Binding var selectedIndex: Int
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(categories.indices, id: \.self) { index in
                    Text(categories[index])
                        .font(.system(size: 15, weight: selectedIndex == index ? .semibold : .regular))
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(selectedIndex == index ? Color.black : Color.white)
                        .foregroundColor(selectedIndex == index ? .white : .black)
                        .cornerRadius(10)
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.black.opacity(selectedIndex == index ? 0 : 0.08), lineWidth: 1)
                        )
                        .onTapGesture {
                            selectedIndex = index
                        }
                }
            }
            .padding(.horizontal, 16) // 左右留白，以免贴边
        }
    }
}

struct UnitPickerView: View {
    let units: [UnitOption]
    @Binding var selectedUnitIndex: Int
    
    var body: some View {
        Menu {
            Picker(L.Tools.Converter.selectUnit, selection: $selectedUnitIndex) {
                ForEach(units.indices, id: \.self) { index in
                    Text(units[index].name).tag(index)
                }
            }
        } label: {
            HStack {
                Text(units[safe: selectedUnitIndex]?.name ?? "-")
                    .font(.system(size: 16))
                    .foregroundColor(.black)
                Spacer()
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
            .background(Color.white)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            .frame(minHeight: 50) // 确保足够高度
        }
    }
}

// Helper to safely access array elements
extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// 隐藏键盘的辅助函数
#if canImport(UIKit)
func hideKeyboard() {
    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
}
#endif

// Preview
struct UnitConverterView_Previews: PreviewProvider {
    static var previews: some View {
        UnitConverterView()
    }
}

// Placeholder for L struct
// struct L {
//    struct Tools {
//        static let converter = "单位换算"
//        struct Converter {
//            static let area = "面积"
//            static let weight = "重量"
//            static let volume = "容量"
//            static let length = "长度"
//            static let temperature = "温度"
//            static let value = "输入数值"
//            static let from = "从"
//            static let to = "到"
//            static let result = "转换结果"
//            static let reference = "常用换算参考"
//            struct Unit {
//                static let squareMeter = "平方米 (m²)"
//                static let mu = "亩"
//                static let hectare = "公顷 (ha)"
//                static let squareKilometer = "平方公里 (km²)"
//                static let acre = "英亩 (acre)"
//                static let kilogram = "千克 (kg)"
//                static let gram = "克 (g)"
//                static let jin = "斤"
//                static let ton = "吨 (t)"
//                static let pound = "磅 (lb)"
//                static let liter = "升 (L)"
//                static let milliliter = "毫升 (mL)"
//                static let cubicMeter = "立方米 (m³)"
//                static let gallon = "加仑 (gal)"
//                static let meter = "米 (m)"
//                static let centimeter = "厘米 (cm)"
//                static let kilometer = "公里 (km)"
//                static let foot = "英尺 (ft)"
//                static let inch = "英寸 (in)"
//                static let celsius = "摄氏度 (°C)"
//                static let fahrenheit = "华氏度 (°F)"
//                static let kelvin = "开尔文 (K)"
//            }
//            struct Reference {
//                static let muToSqM = "1 亩 ≈ 666.67 平方米"
//                static let hectareToSqM = "1 公顷 = 10000 平方米"
//                static let acreToSqM = "1 英亩 ≈ 4046.86 平方米"
//                static let kgToJin = "1 千克 = 2 斤"
//                static let tonToKg = "1 吨 = 1000 千克"
//                static let poundToKg = "1 磅 ≈ 0.453592 千克"
//                static let literToMl = "1 升 = 1000 毫升"
//                static let gallonToLiter = "1 加仑 (美制) ≈ 3.785 升"
//                static let kmToM = "1 公里 = 1000 米"
//                static let footToM = "1 英尺 ≈ 0.3048 米"
//                static let inchToCm = "1 英寸 = 2.54 厘米"
//            }
//        }
//    }
// } 