import Foundation

class NetworkManager {
    static let shared = NetworkManager()
    
    private init() {
        print("NetworkManager 初始化")
    }
    
    /// 测试网络连接，触发iOS的网络权限请求
    func testNetworkConnection() {
        guard let url = URL(string: "https://www.apple.com") else { return }
        
        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            if let error = error {
                print("网络请求失败: \(error.localizedDescription)")
                return
            }
            
            if let httpResponse = response as? HTTPURLResponse {
                print("网络测试成功，状态码: \(httpResponse.statusCode)")
            }
        }
        
        task.resume()
    }
} 