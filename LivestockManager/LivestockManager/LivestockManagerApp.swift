//
//  LivestockManagerApp.swift
//  LivestockManager
//
//  Created by cameron chen on 2025/5/27.
//

import SwiftUI

@main
struct LivestockManagerApp: App {
    @StateObject private var dataManager = AnimalDataManager()
    @State private var languageCode: String = Locale.current.language.languageCode?.identifier ?? "en"
    
    // 数据迁移助手
    private var migrationHelper: DataMigrationHelper?
    
    // 初始化通知管理器
    private let notificationManager = NotificationManager.shared
    
    // 初始化用户设置管理器
    private let userSettingsManager = UserSettingsManager.shared
    
    // 初始化网络管理器
    private let networkManager = NetworkManager.shared
    
    init() {
        // 检测系统语言，并设置应用语言
        if let languageCode = Locale.current.language.languageCode?.identifier {
            UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
            print("检测到系统语言: \(languageCode)")
        }
        
        // 创建数据管理器的实例
        let dataManagerInstance = AnimalDataManager()
        
        // 初始化数据迁移助手
        self.migrationHelper = DataMigrationHelper(dataManager: dataManagerInstance)
        
        // 设置为_dataManager的初始值
        _dataManager = StateObject(wrappedValue: dataManagerInstance)
        
        // 在应用启动时立即触发网络请求以请求网络权限
        DispatchQueue.main.async {
            NetworkManager.shared.testNetworkConnection()
        }
        
        // 在应用启动时检查是否需要初始化通知
        if UserDefaults.standard.bool(forKey: "isFirstLaunch") == false {
            UserDefaults.standard.set(true, forKey: "isFirstLaunch")
            
            // 如果是第一次启动应用，设置默认通知为开启状态并请求权限
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                NotificationManager.shared.requestAuthorization { granted in
                    print("通知权限请求结果: \(granted ? "授予" : "拒绝")")
                }
            }
        }
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(dataManager)
                .preferredColorScheme(.light)
                .onAppear {
                    // 执行数据修复和迁移
                    DispatchQueue.main.async {
                        self.migrationHelper?.repairData()
                        self.migrationHelper?.performMigrationsIfNeeded()
                    }
                }
        }
    }
}
