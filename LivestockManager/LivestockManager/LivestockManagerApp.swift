//
//  LivestockManagerApp.swift
//  LivestockManager
//
//  Created by cameron chen on 2025/5/27.
//

import SwiftUI

@main
struct LivestockManagerApp: App {
    @StateObject private var dataManager = AnimalDataManager()
    @State private var languageCode: String = Locale.current.language.languageCode?.identifier ?? "en"
    
    // 数据迁移助手
    private var migrationHelper: DataMigrationHelper?
    
    // 初始化通知管理器
    private let notificationManager = NotificationManager.shared
    
    // 初始化用户设置管理器
    private let userSettingsManager = UserSettingsManager.shared
    
    init() {
        // 检测系统语言，并设置应用语言
        if let languageCode = Locale.current.language.languageCode?.identifier {
            UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
            print("检测到系统语言: \(languageCode)")
        }
        
        // 创建数据管理器的实例
        let dataManagerInstance = AnimalDataManager()
        
        // 初始化数据迁移助手
        self.migrationHelper = DataMigrationHelper(dataManager: dataManagerInstance)
        
        // 设置为_dataManager的初始值
        _dataManager = StateObject(wrappedValue: dataManagerInstance)
        
        // 在应用启动时检查是否需要初始化通知
        if UserDefaults.standard.bool(forKey: "isFirstLaunch") == false {
            UserDefaults.standard.set(true, forKey: "isFirstLaunch")
            
            // 如果是第一次启动应用，设置默认通知为开启状态并请求权限
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                NotificationManager.shared.requestAuthorization { granted in
                    print("通知权限请求结果: \(granted ? "授予" : "拒绝")")
                }
            }
        }
    }
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(dataManager)
                .onAppear {
                    print("=========================================")
                    print("🚀 应用启动 - LivestockManager")
                    print("=========================================")
                    // 打印当前语言环境信息用于调试
                    print("当前语言: \(Locale.current.language.languageCode?.identifier ?? "未知")")
                    print("首选语言: \(UserDefaults.standard.array(forKey: "AppleLanguages") as? [String] ?? [])")
                    
                    // 执行数据修复和迁移
                    DispatchQueue.main.async {
                        self.migrationHelper?.repairData()
                        self.migrationHelper?.performMigrationsIfNeeded()
                        
                        print("数据迁移和修复已完成")
                    }
                    
                    // 打印数据管理器中的动物数量
                    print("数据管理器中的牲畜数量: \(dataManager.livestock.count)")
                    print("文档目录: \(FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!.path)")
                    print("=========================================")
                }
        }
    }
}
