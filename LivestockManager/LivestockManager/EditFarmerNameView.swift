import SwiftUI

struct EditFarmerNameView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var userSettings = UserSettingsManager.shared
    @State private var newName: String = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField(L.Profile.userTitle, text: $newName)
                        .font(.system(size: 16))
                }
            }
            .navigationTitle(L.Profile.edit)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(L.cancel) {
                        dismiss()
                    }
                    .font(.system(size: 16))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(L.save) {
                        if !newName.isEmpty {
                            userSettings.farmerName = newName
                        }
                        dismiss()
                    }
                    .font(.system(size: 16))
                }
            }
        }
        .onAppear {
            newName = userSettings.farmerName
        }
    }
} 