import SwiftUI

struct StatisticsCardView: View {
    let statistics: AnimalStatistics
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                StatisticItem(
                    number: statistics.inStock,
                    label: L.Stats.inStock,
                    iconName: "circle.fill",
                    weight: .black
                )
                
                // 添加垂直分隔线
                Rectangle()
                    .fill(Color.black.opacity(0.05))
                    .frame(width: 1)
                    .padding(.vertical, 12)
                
                StatisticItem(
                    number: statistics.forSale,
                    label: L.Stats.forSale,
                    iconName: "tag",
                    weight: .medium
                )
                
                // 添加垂直分隔线
                Rectangle()
                    .fill(Color.black.opacity(0.05))
                    .frame(width: 1)
                    .padding(.vertical, 12)
                
                StatisticItem(
                    number: statistics.sold,
                    label: L.Stats.sold,
                    iconName: "checkmark.circle",
                    weight: .light
                )
                
                // 添加垂直分隔线
                Rectangle()
                    .fill(Color.black.opacity(0.05))
                    .frame(width: 1)
                    .padding(.vertical, 12)
                
                StatisticItem(
                    number: statistics.dead,
                    label: <PERSON><PERSON>.deceased,
                    iconName: "xmark.circle",
                    weight: .ultraLight
                )
            }
        }
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.08), lineWidth: 1)
        )
    }
}

struct StatisticItem: View {
    let number: Int
    let label: String
    let iconName: String
    let weight: Font.Weight
    
    var body: some View {
        VStack(spacing: 4) {
            // 添加图标
            Image(systemName: iconName)
                .font(.system(size: 16))
                .foregroundColor(.black)
                .padding(.bottom, 2)
            
            Text("\(number)")
                .font(.system(size: 22, weight: .bold, design: .default))
                .foregroundColor(.black)
            
            Text(label)
                .font(.system(size: 11, weight: .regular))
                .foregroundColor(Color.black.opacity(0.6))
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
    }
}

#Preview {
    StatisticsCardView(statistics: AnimalStatistics(
        inStock: 24,
        forSale: 3,
        sold: 8,
        dead: 1
    ))
    .padding()
    .background(Color(hex: "#F8F8F8"))
} 