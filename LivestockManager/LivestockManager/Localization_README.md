# 多语言本地化配置指南

本项目已添加了多语言支持，包括英文和中文简体。以下是完成配置的步骤：

## 项目配置

1. 在Xcode中打开项目后，选择项目文件 -> TARGETS -> "LivestockManager" -> "Info" 标签页
2. 在 "Localizations" 部分，确保添加了以下语言：
   - English (Base language)
   - Chinese (Simplified) - 添加时选择需要本地化的资源

## 文件结构

本地化资源已组织在以下位置：

- `Resources/en.lproj/Localizable.strings` - 英文字符串
- `Resources/zh-Hans.lproj/Localizable.strings` - 中文简体字符串
- `InfoPlist/InfoPlist.xcstrings` - 应用名称等Info.plist本地化

## 代码使用

项目使用了 `StringExtension.swift` 中定义的 `L` 枚举进行本地化字符串的访问，例如：

```swift
// 直接使用
Text(L.Home.emptyWelcome)

// 或通过String扩展
Text("app.name".localized)
```

## 枚举本地化

Models.swift中的枚举已修改为支持本地化：

1. 枚举的rawValue使用英文标识符（如 "cattle"、"male"）
2. 通过重写displayName或rawValue属性提供本地化显示文本

## 测试本地化

要测试不同语言：

1. 模拟器中：设置 -> 通用 -> 语言与地区 -> iPhone语言 -> 切换语言
2. 或在scheme中设置：Edit Scheme -> Options -> App Language

## 数据迁移说明

由于枚举的原始值已从中文改为英文标识符（例如从"牛"改为"cattle"），对于现有数据需要注意：

1. 首次使用更新后的应用时，可能需要进行数据迁移
2. 建议在第一次启动时检查并更新存储的枚举值，确保与新的rawValue匹配

## 已知问题

- 某些UI元素可能尚未完全本地化，请根据需要继续更新
- 在编辑现有数据时，需要确保存储的枚举值与修改后的rawValue匹配 