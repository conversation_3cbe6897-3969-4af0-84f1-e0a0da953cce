import SwiftUI

/// 应用颜色系统 - 黑白简约扁平风格
struct AppColors {
    // 主色调
    static let primary = Color.black
    static let secondary = Color(hex: "#333333")
    
    // 背景色
    static let background = Color.white
    static let surfaceLight = Color(hex: "#F8F8F8")
    static let surfaceMedium = Color(hex: "#EFEFEF")
    
    // 状态色（灰度系列）
    static let active = Color(hex: "#1A1A1A")
    static let forSale = Color(hex: "#404040")
    static let sold = Color(hex: "#909090")
    static let deceased = Color(hex: "#626262")
    static let other = Color(hex: "#787878")
    
    // 文字色
    static let textPrimary = Color.black
    static let textSecondary = Color(hex: "#555555")
    static let textTertiary = Color(hex: "#888888")
    
    // 边框色
    static let border = Color.black.opacity(0.08)
    static let divider = Color.black.opacity(0.05)
    
    // 强调和互动元素
    static let actionBackground = Color.black.opacity(0.05)
    static let actionForeground = Color.black
    
    // 阴影
    static let shadowLight = Color.black.opacity(0.04)
    static let shadowMedium = Color.black.opacity(0.08)
}

// 十六进制颜色代码扩展
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
} 