import Foundation
import SwiftUI

/// 数据迁移助手，用于处理模型变更和数据格式修复
class DataMigrationHelper {
    private let userDefaults = UserDefaults.standard
    private let migrationVersionKey = "dataMigrationVersion"
    
    /// 当前迁移版本
    private let currentVersion = 1
    
    /// 数据管理器的引用
    private let dataManager: AnimalDataManager
    
    /// 文件URL
    private var fileURL: URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsDirectory.appendingPathComponent("livestock_data.json")
    }
    
    init(dataManager: AnimalDataManager) {
        self.dataManager = dataManager
    }
    
    /// 检查并执行需要的数据迁移
    func performMigrationsIfNeeded() {
        let lastMigrationVersion = userDefaults.integer(forKey: migrationVersionKey)
        
        // 如果已经是最新版本，无需迁移
        if lastMigrationVersion >= currentVersion {
            return
        }
        
        // 执行迁移操作
        if lastMigrationVersion < 1 {
            if migrateToVersion1() {
                print("✅ 迁移到版本1成功")
            } else {
                print("❌ 迁移到版本1失败")
                print("========== 数据迁移失败 ==========")
                return
            }
        }
        
        // 更新迁移版本记录
        userDefaults.set(currentVersion, forKey: migrationVersionKey)
    }
    
    /// 尝试修复数据格式问题
    func repairData() {
        print("========== 开始数据修复检查 ==========")
        
        if FileManager.default.fileExists(atPath: fileURL.path) {
            print("📂 发现数据文件，检查是否需要修复...")
            
            // 尝试直接加载数据，如果成功则不需要修复
            do {
                let data = try Data(contentsOf: fileURL)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                // 尝试解码，如果成功则不需要修复
                _ = try decoder.decode([Animal].self, from: data)
                print("✅ 数据格式正确，无需修复")
            } catch {
                print("⚠️ 数据格式存在问题，尝试修复...")
                print("  错误: \(error.localizedDescription)")
                
                if tryLoadAndFixCorruptedData() {
                    print("✅ 数据修复成功")
                    
                    // 重新加载数据
                    dataManager.forceReloadData()
                } else {
                    print("❌ 数据修复失败")
                }
            }
        } else {
            print("📝 未找到数据文件，无需修复")
        }
        
        print("========== 数据修复检查完成 ==========")
    }
    
    /// 迁移到版本1：处理枚举值从中文变为英文标识符
    private func migrateToVersion1() -> Bool {
        return tryLoadAndFixCorruptedData()
    }
    
    // 尝试加载并修复错误格式的数据
    private func tryLoadAndFixCorruptedData() -> Bool {
        print("🔄 尝试加载并修复损坏的数据...")
        
        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            print("❌ 没有找到数据文件，无法修复")
            return false
        }
        
        do {
            // 读取原始JSON数据
            let jsonData = try Data(contentsOf: fileURL)
            print("✅ 成功读取数据文件，大小: \(jsonData.count) 字节")
            
            // 尝试解析为字典数组
            guard let jsonArray = try? JSONSerialization.jsonObject(with: jsonData, options: []) as? [[String: Any]] else {
                print("❌ 无法将数据解析为JSON对象")
                return false
            }
            
            print("✅ 成功解析为JSON对象，包含 \(jsonArray.count) 条记录")
            
            // 修复的动物数组
            var fixedAnimals: [Animal] = []
            
            // 遍历每个动物记录
            for (index, animalDict) in jsonArray.enumerated() {
                print("🔄 修复记录 #\(index + 1)...")
                
                // 提取ID和visualId
                guard let idString = animalDict["id"] as? String,
                      let id = UUID(uuidString: idString),
                      let visualId = animalDict["visualId"] as? String else {
                    print("❌ 记录 #\(index + 1) 缺少必要的ID字段，跳过")
                    continue
                }
                
                // 设置默认值
                var species = Species.other
                var sex = Sex.male
                var status = AnimalStatus.active
                var source = AnimalSource.selfBred
                
                // 提取并修复物种字段
                if let speciesRaw = animalDict["species"] as? String {
                    if speciesRaw == "牛" {
                        species = Species.cattle
                    } else if speciesRaw == "羊" {
                        species = Species.sheep
                    } else if speciesRaw == "猪" {
                        species = Species.pig
                    } else if speciesRaw == "马" {
                        species = Species.horse
                    } else if speciesRaw == "鸡" {
                        species = Species.chicken
                    } else if speciesRaw == "鸭" {
                        species = Species.duck
                    } else if speciesRaw == "鹅" {
                        species = Species.goose
                    } else {
                        species = Species.other
                    }
                }
                
                // 提取并修复性别字段
                if let sexRaw = animalDict["sex"] as? String {
                    if sexRaw == "公" || sexRaw == "male" {
                        sex = Sex.male
                    } else if sexRaw == "母" || sexRaw == "female" {
                        sex = Sex.female
                    } else {
                        sex = Sex.castrated
                    }
                }
                
                // 提取并修复状态字段
                if let statusRaw = animalDict["status"] as? String {
                    if statusRaw == "存栏" || statusRaw == "active" {
                        status = AnimalStatus.active
                    } else if statusRaw == "待售" || statusRaw == "forSale" {
                        status = AnimalStatus.forSale
                    } else if statusRaw == "已售" || statusRaw == "sold" {
                        status = AnimalStatus.sold
                    } else if statusRaw == "死亡" || statusRaw == "deceased" {
                        status = AnimalStatus.deceased
                    } else {
                        status = AnimalStatus.other
                    }
                }
                
                // 提取并修复来源字段
                if let sourceRaw = animalDict["source"] as? String {
                    if sourceRaw == "自繁" || sourceRaw == "selfBred" {
                        source = AnimalSource.selfBred
                    } else {
                        source = AnimalSource.purchased
                    }
                }
                
                // 解析日期
                var birthDate: Date? = nil
                var createdAt = Date()
                var updatedAt = Date()
                
                if let birthDateStr = animalDict["birthDate"] as? String {
                    let formatter = ISO8601DateFormatter()
                    if let date = formatter.date(from: birthDateStr) {
                        birthDate = date
                    }
                }
                
                if let createdAtStr = animalDict["createdAt"] as? String {
                    let formatter = ISO8601DateFormatter()
                    if let date = formatter.date(from: createdAtStr) {
                        createdAt = date
                    }
                }
                
                if let updatedAtStr = animalDict["updatedAt"] as? String {
                    let formatter = ISO8601DateFormatter()
                    if let date = formatter.date(from: updatedAtStr) {
                        updatedAt = date
                    }
                }
                
                // 提取其他字段
                let photoPath = animalDict["photoPath"] as? String
                let breed = animalDict["breed"] as? String
                let notes = animalDict["notes"] as? String
                
                // 处理购买详情
                var purchaseDetails: PurchaseDetails? = nil
                if let purchaseDict = animalDict["purchaseDetails"] as? [String: Any] {
                    var price: Double? = nil
                    var date: Date? = nil
                    var currency: String? = "CNY"
                    
                    if let priceValue = purchaseDict["price"] as? Double {
                        price = priceValue
                    }
                    
                    if let dateStr = purchaseDict["date"] as? String {
                        let formatter = ISO8601DateFormatter()
                        if let parsedDate = formatter.date(from: dateStr) {
                            date = parsedDate
                        }
                    }
                    
                    if let currencyValue = purchaseDict["currency"] as? String {
                        currency = currencyValue
                    }
                    
                    purchaseDetails = PurchaseDetails(date: date, price: price, currency: currency)
                }
                
                // 创建Animal对象
                let animal = Animal(
                    id: id,
                    visualId: visualId,
                    photoPath: photoPath,
                    species: species,
                    breed: breed,
                    sex: sex,
                    birthDate: birthDate,
                    source: source,
                    purchaseDetails: purchaseDetails,
                    status: status,
                    notes: notes,
                    createdAt: createdAt,
                    updatedAt: updatedAt
                )
                
                // 添加到修复后的数组
                fixedAnimals.append(animal)
                print("✅ 记录 #\(index + 1) 修复成功")
            }
            
            // 更新内存中的数据
            dataManager.replaceAllAnimals(with: fixedAnimals)
            print("✅ 成功修复 \(fixedAnimals.count) 条记录")
            
            return true
        } catch {
            print("❌ 修复数据时发生错误: \(error.localizedDescription)")
            return false
        }
    }
} 