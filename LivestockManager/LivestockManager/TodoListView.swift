import SwiftUI

struct TodoItem: Identifiable, Equatable, Codable {
    var id = UUID()
    var title: String
    var createdAt: Date
    var isCompleted: Bool = false
}

class TodoListViewModel: ObservableObject {
    @Published var todoItems: [TodoItem] = []
    @Published var newTodoTitle: String = ""
    @Published var showCompletedTasks: Bool = true
    
    private let saveKey = "farm_todo_items"
    
    init() {
        loadTodoItems()
        
        // 如果没有加载到数据，则添加示例数据
        if todoItems.isEmpty {
            // 预置一些示例数据
            let exampleItems = [
                TodoItem(title: L.Todo.exampleTask1, createdAt: Date().addingTimeInterval(-86400)),
                TodoItem(title: L.Todo.exampleTask2, createdAt: Date().addingTimeInterval(-43200)),
                TodoItem(title: L.Todo.exampleTask3, createdAt: Date())
            ]
            todoItems = exampleItems
            saveTodoItems()
        }
    }
    
    var sortedTodoItems: [TodoItem] {
        let activeItems = todoItems.filter { !$0.isCompleted }
        let completedItems = todoItems.filter { $0.isCompleted }
        
        if showCompletedTasks {
            return activeItems.sorted(by: { $0.createdAt > $1.createdAt }) + 
                   completedItems.sorted(by: { $0.createdAt > $1.createdAt })
        } else {
            return activeItems.sorted(by: { $0.createdAt > $1.createdAt })
        }
    }
    
    func addTodoItem() {
        guard !newTodoTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let newItem = TodoItem(title: newTodoTitle, createdAt: Date())
        todoItems.append(newItem)
        newTodoTitle = ""
        
        saveTodoItems()
    }
    
    func toggleItemCompletion(_ item: TodoItem) {
        if let index = todoItems.firstIndex(where: { $0.id == item.id }) {
            todoItems[index].isCompleted.toggle()
            saveTodoItems()
        }
    }
    
    func deleteTodoItem(_ item: TodoItem) {
        todoItems.removeAll { $0.id == item.id }
        saveTodoItems()
    }
    
    func toggleShowCompletedTasks() {
        showCompletedTasks.toggle()
    }
    
    // MARK: - 数据持久化
    private func saveTodoItems() {
        if let encoded = try? JSONEncoder().encode(todoItems) {
            UserDefaults.standard.set(encoded, forKey: saveKey)
        }
    }
    
    private func loadTodoItems() {
        if let savedItems = UserDefaults.standard.data(forKey: saveKey) {
            if let decodedItems = try? JSONDecoder().decode([TodoItem].self, from: savedItems) {
                todoItems = decodedItems
                return
            }
        }
        todoItems = []
    }
}

struct TodoListView: View {
    @StateObject private var viewModel = TodoListViewModel()
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航栏
            TodoNavBar(
                showCompleted: viewModel.showCompletedTasks,
                onToggleShowCompleted: viewModel.toggleShowCompletedTasks
            )
            
            // 主内容区
            VStack(spacing: 16) {
                // 添加新代办
                HStack {
                    TextField(L.Todo.addPlaceholder, text: $viewModel.newTodoTitle)
                        .padding(.vertical, 12)
                        .padding(.horizontal, 14)
                        .background(Color.white)
                        .cornerRadius(8)
                        .shadow(color: Color.black.opacity(0.03), radius: 1, x: 0, y: 1)
                        .submitLabel(.done)
                        .onSubmit {
                            viewModel.addTodoItem()
                        }
                    
                    Button(action: viewModel.addTodoItem) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.black)
                    }
                    .disabled(viewModel.newTodoTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal, 8)
                .padding(.top, 16)
                
                // 代办列表
                if viewModel.todoItems.isEmpty {
                    emptyStateView
                } else if viewModel.sortedTodoItems.isEmpty && !viewModel.showCompletedTasks {
                    VStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                            .padding(.top, 40)
                        
                        Text(L.Todo.allCompleted)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.gray)
                        
                        Button(L.Todo.viewCompleted) {
                            viewModel.toggleShowCompletedTasks()
                        }
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.black)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.white)
                        .cornerRadius(8)
                        .padding(.top, 16)
                        .padding(.bottom, 40)
                    }
                    .frame(maxWidth: .infinity)
                } else {
                    List {
                        ForEach(viewModel.sortedTodoItems) { item in
                            TodoItemRow(item: item) { item in
                                viewModel.toggleItemCompletion(item)
                            } onDelete: { item in
                                viewModel.deleteTodoItem(item)
                            }
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                            .swipeActions(edge: .trailing) {
                                Button(role: .destructive) {
                                    viewModel.deleteTodoItem(item)
                                } label: {
                                    Label(L.delete, systemImage: "trash")
                                }
                            }
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(.systemGray6))
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "checklist")
                .font(.system(size: 50))
                .foregroundColor(.gray)
                .padding(.top, 40)
            
            Text(L.Todo.empty)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.gray)
            
            Text(L.Todo.emptyDesc)
                .font(.system(size: 14))
                .foregroundColor(.gray)
                .padding(.bottom, 40)
        }
        .frame(maxWidth: .infinity)
    }
}

struct TodoNavBar: View {
    let showCompleted: Bool
    let onToggleShowCompleted: () -> Void
    
    var body: some View {
        HStack {
            Text(L.Todo.title)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.black)
            
            Spacer()
            
            Button(action: onToggleShowCompleted) {
                HStack(spacing: 4) {
                    Image(systemName: showCompleted ? "eye" : "eye.slash")
                        .font(.system(size: 14))
                    
                    Text(showCompleted ? L.Todo.hideCompleted : L.Todo.showCompleted)
                        .font(.system(size: 14))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color(.systemGray6))
                .cornerRadius(16)
            }
            .foregroundColor(.black)
        }
        .padding()
        .background(Color.white)
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
    }
}

struct TodoItemRow: View {
    let item: TodoItem
    let onToggle: (TodoItem) -> Void
    let onDelete: (TodoItem) -> Void
    
    var body: some View {
        HStack {
            // 完成状态按钮
            Button(action: { onToggle(item) }) {
                Image(systemName: item.isCompleted ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 20))
                    .foregroundColor(item.isCompleted ? .black : .gray)
            }
            .padding(.trailing, 8)
            
            // 内容区
            VStack(alignment: .leading, spacing: 4) {
                Text(item.title)
                    .font(.system(size: 16))
                    .foregroundColor(item.isCompleted ? .gray : .black)
                    .strikethrough(item.isCompleted)
                
                Text(formattedDate(item.createdAt))
                    .font(.system(size: 12))
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            // 删除按钮（在非滑动状态下隐藏）
            if item.isCompleted {
                Button(action: { onDelete(item) }) {
                    Image(systemName: "trash")
                        .font(.system(size: 16))
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 14)
        .background(Color.white)
        .cornerRadius(8)
        .shadow(color: Color.black.opacity(0.03), radius: 1, x: 0, y: 1)
        .padding(.horizontal, 8)
    }
    
    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        // 根据当前语言环境设置日期格式
        let languageCode = Locale.current.language.languageCode?.identifier
        
        if languageCode == "en" {
            formatter.dateFormat = "MMM d, yyyy HH:mm"
        } else if languageCode == "nl" {
            formatter.dateFormat = "d MMM yyyy HH:mm"
        } else {
            formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        }
        
        return formatter.string(from: date)
    }
}

#Preview {
    TodoListView()
} 