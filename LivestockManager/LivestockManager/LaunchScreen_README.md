# LivestockManager 开屏页设计说明

## 设计概述

为LivestockManager应用创建了一个符合应用风格的专业开屏页，采用黑白扁平化设计，简洁而有设计感。

## 设计特点

### 🎨 视觉设计
- **配色方案**: 纯白背景 + 黑色文字，符合AppColors.swift中定义的颜色系统
- **设计风格**: 扁平化设计，无阴影和渐变效果
- **布局**: 居中对称布局，专业简洁

### 📱 界面元素
1. **应用图标**: 
   - 80x80像素，居中显示
   - 使用LaunchIcon图像集，支持多分辨率
   - 位于屏幕中心稍上方位置

2. **应用名称**: 
   - 24pt中等字重系统字体
   - 黑色文字，居中对齐
   - 支持多语言显示

3. **副标题**: 
   - 16pt轻字重系统字体
   - 深灰色文字 (#333333)
   - 描述应用功能特点

### 🌍 多语言支持
- **中文**: "牧场管理" / "专业的牲畜管理工具"
- **英文**: "Livestock Manager" / "Professional Livestock Management Tool"
- 通过LaunchScreen.strings文件实现本地化

### 📐 布局约束
- 应用图标容器: 100x100像素，屏幕水平居中，垂直居中偏上20像素
- 应用名称: 距离图标容器底部20像素，左右边距40像素
- 副标题: 距离应用名称底部10像素，左右边距40像素
- 所有元素支持自适应布局，适配不同设备尺寸

## 技术实现

### 文件结构
```
LivestockManager/
├── LaunchScreen.storyboard                    # 启动屏幕界面文件
├── Assets.xcassets/
│   └── LaunchIcon.imageset/                   # 启动屏幕专用图标
│       ├── Contents.json
│       ├── iconV1.png
│       ├── <EMAIL>
│       └── <EMAIL>
└── Resources/
    ├── en.lproj/LaunchScreen.strings          # 英文本地化
    └── zh-Hans.lproj/LaunchScreen.strings     # 中文本地化
```

### 项目配置
- 更新了project.pbxproj文件，将启动屏幕配置从程序化生成改为使用storyboard
- 设置 `INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen`

## 设计理念

### 专业性
- 简洁的黑白配色体现专业性
- 清晰的层次结构便于用户理解
- 符合现代iOS应用设计规范

### 一致性
- 与应用主体设计风格保持一致
- 使用相同的颜色系统和字体规范
- 图标风格与应用图标统一

### 用户体验
- 快速加载，无复杂动画
- 清晰的品牌识别
- 多语言支持提升国际化体验

## 使用说明

1. **编译运行**: 项目会自动使用新的启动屏幕
2. **多语言测试**: 在设备设置中切换语言可测试本地化效果
3. **设备适配**: 启动屏幕会自动适配不同iPhone和iPad尺寸

## 维护说明

- 如需修改文字内容，请同时更新对应的LaunchScreen.strings文件
- 如需更换图标，请替换LaunchIcon.imageset中的图片文件
- 布局调整请在LaunchScreen.storyboard中进行
