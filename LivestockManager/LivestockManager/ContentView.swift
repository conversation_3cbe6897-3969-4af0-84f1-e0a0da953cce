//
//  ContentView.swift
//  LivestockManager
//
//  Created by cameron chen on 2025/5/27.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var dataManager: AnimalDataManager
    @State private var selectedTab = 0
    @State private var showingSearch = false
    @State private var showingFilter = false
    @State private var showingAddAnimal = false
    @State private var showingCamera = false
    @State private var selectedAnimal: Animal? = nil
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                Color(hex: "#F8F8F8")
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 根据选中的tab显示不同内容
                    if selectedTab == 0 {
                        // 首页内容
                        VStack(spacing: 0) {
                            // 顶部导航栏
                            TopNavigationView(
                                onSearchTapped: { showingSearch = true },
                                onFilterTapped: { showingFilter = true },
                                hasActiveFilters: dataManager.hasActiveFilters
                            )
                            
                            // 筛选指示器
                            if dataManager.hasActiveFilters {
                                FilterIndicator(onClear: {
                                    dataManager.resetFilters()
                                })
                            }
                            
                            // 主内容区
                            if dataManager.livestock.isEmpty {
                                // 空状态页面
                                EmptyStateView(onAddAnimal: { showingAddAnimal = true })
                            } else {
                                ScrollView(showsIndicators: false) {
                                    LazyVStack(spacing: 18) {
                                        // 统计卡片
                                        StatisticsCardView(statistics: dataManager.statistics)
                                            .padding(.horizontal, 16)
                                            .padding(.top, 18)
                                        
                                        // 筛选结果为空的提示
                                        if dataManager.filteredLivestock.isEmpty && dataManager.hasActiveFilters {
                                            NoFilterResultsView(onClear: {
                                                dataManager.resetFilters()
                                            })
                                            .padding(.horizontal, 16)
                                            .padding(.top, 8)
                                        }
                                        
                                        // 牲畜列表
                                        LazyVStack(spacing: 14) {
                                            ForEach(dataManager.filteredLivestock) { livestock in
                                                AnimalCardView(livestock: livestock) {
                                                    // 点击牲畜卡片的处理
                                                    print("点击了动物卡片: \(livestock.visualId), ID: \(livestock.id)")
                                                    selectedAnimal = nil // 先设为nil再赋值，确保触发id变化
                                                    DispatchQueue.main.async {
                                                        selectedAnimal = livestock
                                                        print("设置selectedAnimal: \(livestock.visualId), ID: \(livestock.id)")
                                                    }
                                                }
                                                .padding(.horizontal, 16)
                                            }
                                        }
                                        
                                        // 底部安全区域
                                        Color.clear
                                            .frame(height: 100)
                                    }
                                }
                                .refreshable {
                                    // 下拉刷新
                                    await refreshData()
                                }
                            }
                        }
                    } else if selectedTab == 1 {
                        // 牧场代办页面
                        TodoListView()
                    } else if selectedTab == 2 {
                        // 设置页面（原个人中心页面）
                        ProfileMainView()
                    } else if selectedTab == 3 {
                        // 工具页面（原搜索页面）
                        ToolsView()
                    }
                }
                
                // 底部导航栏
                VStack {
                    Spacer()
                    BottomTabView(
                        selectedTab: $selectedTab,
                        onAddTapped: { showingAddAnimal = true },
                        onProfileTapped: { selectedTab = 2 }
                    )
                }
                .ignoresSafeArea(.container, edges: .bottom)
            }
        }
        .navigationBarHidden(true)
        .preferredColorScheme(.light)
        .sheet(isPresented: $showingFilter) {
            FilterView()
                .environmentObject(dataManager)
                .preferredColorScheme(.light)
        }
        .sheet(isPresented: $showingAddAnimal) {
            AddAnimalView()
                .environmentObject(dataManager)
                .preferredColorScheme(.light)
        }
        .sheet(isPresented: $showingSearch) {
            SearchView()
                .environmentObject(dataManager)
                .preferredColorScheme(.light)
        }
        .sheet(isPresented: $showingCamera) {
            Text("相机功能将在这里实现")
                .padding()
                .preferredColorScheme(.light)
        }
        .sheet(item: $selectedAnimal, onDismiss: {
            print("详情页被关闭")
        }) { animal in
            NavigationView {
                AnimalDetailView(livestock: animal)
                    .environmentObject(dataManager)
                    .id(animal.id)
                    .preferredColorScheme(.light)
            }
            .onAppear {
                print("详情页NavigationView出现，动物ID: \(animal.id)")
            }
        }
        .onAppear {
            startMonitoringConsoleErrors()
        }
    }
    
    private func refreshData() async {
        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        // 这里可以添加实际的数据刷新逻辑
        dataManager.forceReloadData() // 重新应用筛选
    }
    
    // 用于捕获控制台输出的扩展
    func startMonitoringConsoleErrors() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("LogErrorMessage"),
            object: nil,
            queue: .main) { notification in
                if let errorMessage = notification.object as? String {
                    print("📝 捕获到错误消息: \(errorMessage)")
                    // 这里可以存储错误消息或更新UI
                }
            }
    }
}

struct TopNavigationView: View {
    let onSearchTapped: () -> Void
    let onFilterTapped: () -> Void
    let hasActiveFilters: Bool
    
    var body: some View {
        HStack {
            // 左侧标题
            HStack(spacing: 8) {
                Image(systemName: "house.fill")
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(.black)
                
                Text(L.navMyFarm)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)
            }
            
            Spacer()
            
            // 右侧按钮
            HStack(spacing: 16) {
                Button(action: onSearchTapped) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.black)
                        .frame(width: 36, height: 36)
                        .background(Color.black.opacity(0.05))
                        .clipShape(Circle())
                }
                
                Button(action: onFilterTapped) {
                    ZStack {
                        Image(systemName: "line.3.horizontal.decrease")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.black)
                            .frame(width: 36, height: 36)
                            .background(Color.black.opacity(0.05))
                            .clipShape(Circle())
                        
                        if hasActiveFilters {
                            Circle()
                                .fill(Color.black)
                                .frame(width: 6, height: 6)
                                .offset(x: 10, y: -10)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.white)
    }
}

// MARK: - 空状态页面
struct EmptyStateView: View {
    let onAddAnimal: () -> Void
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                Spacer()
                    .frame(height: 40)
                
                // 主要图标和欢迎信息
                VStack(spacing: 24) {
                    // 牧场图标 - 黑白风格
                    ZStack {
                        Circle()
                            .fill(Color.black.opacity(0.05))
                            .frame(width: 180, height: 180)
                        
                        Image(systemName: "house")
                            .font(.system(size: 70, weight: .light))
                            .foregroundColor(.black)
                    }
                    
                    VStack(spacing: 16) {
                        Text(L.Home.emptyWelcome)
                            .font(.system(size: 22, weight: .semibold))
                            .foregroundColor(.black)
                            .multilineTextAlignment(.center)
                        
                        Text(L.Home.emptyNoAnimals)
                            .font(.system(size: 16))
                            .foregroundColor(Color.black.opacity(0.6))
                            .multilineTextAlignment(.center)
                            .lineSpacing(4)
                    }
                    .padding(.horizontal, 32)
                }
                
                // 添加按钮 - 黑白风格
                Button(action: onAddAnimal) {
                    HStack(spacing: 10) {
                        Image(systemName: "plus")
                            .font(.system(size: 16, weight: .semibold))
                        
                        Text(L.Home.emptyAddFirst)
                            .font(.system(size: 16, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(height: 54)
                    .frame(maxWidth: .infinity)
                    .background(Color.black)
                    .cornerRadius(12)
                }
                .padding(.horizontal, 32)
                .padding(.top, 16)
            }
            .padding(.bottom, 100)
        }
    }
}

// MARK: - 功能介绍行
struct FeatureRow: View {
    let systemIcon: String
    let iconColor: Color
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: systemIcon)
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(iconColor)
                .frame(width: 48, height: 48)
                .background(iconColor.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 12))
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
}

// MARK: - 小贴士行
struct TipRow: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text("•")
                .foregroundColor(.blue)
                .font(.system(size: 14, weight: .bold))
            
            Text(text)
                .font(.system(size: 14))
                .foregroundColor(.primary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

// MARK: - 筛选指示器
struct FilterIndicator: View {
    let onClear: () -> Void
    
    var body: some View {
        HStack {
            // 筛选状态指示
            HStack(spacing: 6) {
                Image(systemName: "line.3.horizontal.decrease")
                    .font(.system(size: 12))
                
                Text(L.Filter.applied)
                    .font(.system(size: 12))
            }
            .foregroundColor(Color.black.opacity(0.6))
            
            Spacer()
            
            // 清除筛选按钮
            Button(action: onClear) {
                Text(L.Filter.clear)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.black)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(Color.black.opacity(0.03))
    }
}

// MARK: - 无筛选结果视图
struct NoFilterResultsView: View {
    let onClear: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // 图标
            Image(systemName: "exclamationmark.magnifyingglass")
                .font(.system(size: 36))
                .foregroundColor(Color.black.opacity(0.6))
                .padding(.bottom, 8)
            
            // 提示文本
            Text(L.Filter.noResults)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.black)
                .multilineTextAlignment(.center)
            
            Text(L.Filter.noResultsHelp)
                .font(.system(size: 14))
                .foregroundColor(Color.black.opacity(0.6))
                .multilineTextAlignment(.center)
                .padding(.bottom, 12)
            
            // 清除筛选按钮
            Button(action: onClear) {
                Text(L.Filter.clearAll)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.black)
                    .cornerRadius(8)
            }
        }
        .padding(24)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.08), lineWidth: 1)
        )
    }
}

#Preview {
    ContentView()
        .environmentObject(AnimalDataManager())
}