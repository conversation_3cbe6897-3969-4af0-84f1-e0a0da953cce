import SwiftUI

struct NotificationSettingsView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text(L.Notification.title)) {
                    Toggle(isOn: $notificationManager.isNotificationEnabled) {
                        Text(L.Notification.daily)
                    }
                    .onChange(of: notificationManager.isNotificationEnabled) { newValue in
                        if newValue {
                            // 请求通知权限
                            notificationManager.requestAuthorization { granted in
                                if !granted {
                                    // 如果权限被拒绝，显示提示
                                    alertTitle = L.Notification.permissionDenied
                                    alertMessage = L.Notification.permissionMessage
                                    showingAlert = true
                                    notificationManager.isNotificationEnabled = false
                                }
                            }
                        }
                    }
                    
                    if notificationManager.isNotificationEnabled {
                        DatePicker(L.Notification.time, selection: $notificationManager.notificationTime, displayedComponents: .hourAndMinute)
                    }
                }
                
                Section(header: Text(L.Notification.systemSettings), footer: Text(L.Notification.systemHint)) {
                    Button(action: {
                        notificationManager.openNotificationSettings()
                    }) {
                        HStack {
                            Text(L.Notification.systemOpen)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                Section(header: Text(L.Notification.about)) {
                    VStack(alignment: .leading, spacing: 10) {
                        Text(L.Notification.content)
                            .font(.headline)
                        Text(L.Notification.message)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle(L.Notification.title)
            .navigationBarItems(leading: Button(L.Profile.back) {
                presentationMode.wrappedValue.dismiss()
            })
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text(alertTitle),
                    message: Text(alertMessage),
                    primaryButton: .default(Text(L.Notification.gotoSettings), action: {
                        notificationManager.openNotificationSettings()
                    }),
                    secondaryButton: .cancel(Text(L.cancel))
                )
            }
        }
        .onAppear {
            // 检查通知权限状态
            notificationManager.checkAuthorizationStatus { isAuthorized in
                if !isAuthorized && notificationManager.isNotificationEnabled {
                    alertTitle = L.Notification.permissionDenied
                    alertMessage = L.Notification.permissionMessage
                    showingAlert = true
                    notificationManager.isNotificationEnabled = false
                }
            }
        }
    }
} 