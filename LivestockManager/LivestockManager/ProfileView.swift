import SwiftUI

// MARK: - 主页面中的个人中心视图
struct ProfileMainView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showEmailPopup = false // 控制邮箱弹窗显示状态
    @State private var showNotificationSettings = false // 控制通知设置视图的显示状态
    @State private var showUsageGuide = false // 新增状态变量
    
    var body: some View {
        ZStack {
            ScrollView {
                VStack(spacing: 0) {
                    // 头部用户信息区域
                    ProfileHeaderContentView()
                    
                    // 统计数据卡片
                    StatisticsSection()
                        .padding(.horizontal, 16)
                        .padding(.top, 20)
                    
                    // 牧场管理区域 - 1.1版本功能，暂时隐藏
                    /*
                    MenuSection(title: L.Profile.farmManagement) {
                        VStack(spacing: 0) {
                            MenuRowSingle(
                                icon: "externaldrive",
                                iconColor: .blue,
                                title: L.Profile.dataBackup,
                                action: { print("数据备份") }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                            
                            MenuRowSingle(
                                icon: "square.and.arrow.up",
                                iconColor: .green,
                                title: L.Profile.dataExport,
                                action: { print("数据导出") }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                            
                            MenuRowSingle(
                                icon: "chart.bar",
                                iconColor: .purple,
                                title: L.Profile.statistics,
                                action: { print("统计报表") }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 24)
                    */
                    
                    // 设置区域
                    MenuSection(title: L.Profile.settings) {
                        VStack(spacing: 0) {
                            MenuRowSingle(
                                icon: "bell",
                                title: L.Profile.notification,
                                action: { showNotificationSettings = true }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                                .foregroundColor(Color.black.opacity(0.05))
                                
                            MenuRowSingle(
                                icon: "globe",
                                title: L.Profile.language,
                                action: { 
                                    if let url = URL(string: UIApplication.openSettingsURLString) {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                                .foregroundColor(Color.black.opacity(0.05))
                            
                            MenuRowSingle(
                                icon: "lock.shield",
                                title: L.Profile.privacy,
                                action: { 
                                    if let url = URL(string: "https://livestock.xzonesoft.com/privacy.html") { 
                                        UIApplication.shared.open(url) 
                                    } 
                                }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                                .foregroundColor(Color.black.opacity(0.05))
                            
                            MenuRowSingle(
                                icon: "info.circle",
                                title: L.Profile.about,
                                action: { 
                                    // 显示版本信息，不执行任何操作
                                }
                            )
                            .overlay(
                                HStack {
                                    Spacer()
                                    Text(getAppVersion())
                                        .font(.system(size: 14))
                                        .foregroundColor(Color.black.opacity(0.5))
                                        .padding(.trailing, 32)
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 24)
                    
                    // 帮助与反馈区域
                    MenuSection(title: L.Profile.help) {
                        VStack(spacing: 0) {
                            MenuRowSingle(
                                icon: "questionmark.circle",
                                title: L.Profile.helpUsage,
                                action: { showUsageGuide = true }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                                .foregroundColor(Color.black.opacity(0.05))
                            
                            MenuRowSingle(
                                icon: "star",
                                title: L.Profile.rate,
                                action: { 
                                    // 跳转到App Store评价页面
                                    let appID = "6746741414"
                                    if let url = URL(string: "https://itunes.apple.com/app/id\(appID)?action=write-review") {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            )
                            
                            Divider()
                                .padding(.leading, 60)
                                .foregroundColor(Color.black.opacity(0.05))
                            
                            MenuRowSingle(
                                icon: "envelope",
                                title: L.Profile.contact,
                                action: { 
                                    // 显示邮箱弹窗
                                    showEmailPopup = true
                                }
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 24)
                    
                    // 底部安全区域
                    Color.clear.frame(height: 100)
                }
            }
            .background(Color(hex: "#F8F8F8"))
            .ignoresSafeArea()
            
            // 邮箱弹窗
            if showEmailPopup {
                ZStack {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showEmailPopup = false
                        }
                    
                    EmailPopupView(
                        emailAddress: "<EMAIL>",
                        isPresented: $showEmailPopup
                    )
                }
                .transition(.opacity)
                .animation(.easeInOut, value: showEmailPopup)
            }
        }
        .sheet(isPresented: $showNotificationSettings) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showUsageGuide) {
            UsageGuideView()
        }
    }
    
    // MARK: - 辅助函数
    // 获取应用版本号（不包含构建号）
    func getAppVersion() -> String {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            return version
        }
        return "1.0"
    }
}

struct ProfileView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showEmailPopup = false // 控制邮箱弹窗显示状态
    @State private var showNotificationSettings = false // 控制通知设置视图的显示状态
    @State private var showUsageGuide = false // 新增状态变量
    
    var body: some View {
        ZStack {
            // 背景色
            Color(hex: "#F8F8F8")
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                            .frame(width: 32, height: 32)
                            .background(Color.white.opacity(0.2))
                            .clipShape(Circle())
                    }
                    
                    Spacer()
                    
                    Text(L.Profile.title)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    // 占位符保持居中
                    Color.clear
                        .frame(width: 32, height: 32)
                }
                .padding(.horizontal, 16)
                .padding(.top, 50)
                .background(
                    Color.black
                )
                
                ScrollView {
                    VStack(spacing: 0) {
                        // 头部用户信息区域
                        ProfileHeaderContentView()
                        
                        // 统计数据卡片
                        StatisticsSection()
                            .padding(.horizontal, 16)
                            .padding(.top, 20)
                        
                        // 设置区域
                        MenuSection(title: L.Profile.settings) {
                            VStack(spacing: 0) {
                                MenuRowSingle(
                                    icon: "bell",
                                    title: L.Profile.notification,
                                    action: { showNotificationSettings = true }
                                )
                                
                                Divider()
                                    .padding(.leading, 60)
                                    .foregroundColor(Color.black.opacity(0.05))
                                
                                MenuRowSingle(
                                    icon: "globe",
                                    title: L.Profile.language,
                                    action: { 
                                        if let url = URL(string: UIApplication.openSettingsURLString) {
                                            UIApplication.shared.open(url)
                                        }
                                    }
                                )
                                
                                Divider()
                                    .padding(.leading, 60)
                                    .foregroundColor(Color.black.opacity(0.05))
                                
                                MenuRowSingle(
                                    icon: "lock.shield",
                                    title: L.Profile.privacy,
                                    action: { 
                                        if let url = URL(string: "https://livestock.xzonesoft.com/privacy.html") { 
                                            UIApplication.shared.open(url) 
                                        } 
                                    }
                                )
                                
                                Divider()
                                    .padding(.leading, 60)
                                    .foregroundColor(Color.black.opacity(0.05))
                                
                                MenuRowSingle(
                                    icon: "info.circle",
                                    title: L.Profile.about,
                                    action: { 
                                        // 显示版本信息，不执行任何操作
                                    }
                                )
                                .overlay(
                                    HStack {
                                        Spacer()
                                        Text(getAppVersion())
                                            .font(.system(size: 14))
                                            .foregroundColor(Color.black.opacity(0.5))
                                            .padding(.trailing, 32)
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 24)
                        
                        // 帮助与反馈区域
                        MenuSection(title: L.Profile.help) {
                            VStack(spacing: 0) {
                                MenuRowSingle(
                                    icon: "questionmark.circle",
                                    title: L.Profile.helpUsage,
                                    action: { showUsageGuide = true }
                                )
                                
                                Divider()
                                    .padding(.leading, 60)
                                    .foregroundColor(Color.black.opacity(0.05))
                                
                                MenuRowSingle(
                                    icon: "star",
                                    title: L.Profile.rate,
                                    action: { 
                                        // 跳转到App Store评价页面
                                        let appID = "6746741414"
                                        if let url = URL(string: "https://itunes.apple.com/app/id\(appID)?action=write-review") {
                                            UIApplication.shared.open(url)
                                        }
                                    }
                                )
                                
                                Divider()
                                    .padding(.leading, 60)
                                    .foregroundColor(Color.black.opacity(0.05))
                                
                                MenuRowSingle(
                                    icon: "envelope",
                                    title: L.Profile.contact,
                                    action: { 
                                        // 显示邮箱弹窗
                                        showEmailPopup = true
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 24)
                        
                        // 底部安全区域
                        Color.clear.frame(height: 100)
                    }
                }
            }
            
            // 邮箱弹窗
            if showEmailPopup {
                ZStack {
                    Color.black.opacity(0.4)
                        .ignoresSafeArea()
                        .onTapGesture {
                            showEmailPopup = false
                        }
                    
                    EmailPopupView(
                        emailAddress: "<EMAIL>",
                        isPresented: $showEmailPopup
                    )
                }
                .transition(.opacity)
                .animation(.easeInOut, value: showEmailPopup)
            }
        }
        .sheet(isPresented: $showNotificationSettings) {
            NotificationSettingsView()
        }
        .sheet(isPresented: $showUsageGuide) {
            UsageGuideView()
        }
    }
    
    // MARK: - 辅助函数
    // 获取应用版本号（不包含构建号）
    func getAppVersion() -> String {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
            return version
        }
        return "1.0"
    }
}

// MARK: - 头部用户信息内容
struct ProfileHeaderContentView: View {
    @ObservedObject private var userSettings = UserSettingsManager.shared
    @State private var showEditName = false
    
    var body: some View {
        ZStack {
            // 黑色背景
            Color.black
                .frame(height: 200)
            
            VStack(spacing: 20) {
                // 顶部标题和编辑按钮
                HStack {
                    Text(L.Profile.title)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Button(action: {
                        showEditName = true
                    }) {
                        Image(systemName: "pencil")
                            .font(.system(size: 18))
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(Color.white.opacity(0.15))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 50)
                
                // 用户信息
                HStack(spacing: 16) {
                    // 头像
                    Circle()
                        .fill(Color.white.opacity(0.15))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(userSettings.farmerName)
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                        
                        Text(L.Profile.userTitle + " · " + String(format: L.Profile.userDays, userSettings.daysUsed))
                            .font(.system(size: 12))
                            .foregroundColor(.white.opacity(0.9))
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                
                Spacer()
            }
        }
        .sheet(isPresented: $showEditName) {
            EditFarmerNameView()
        }
    }
}

// MARK: - 统计数据区域
struct StatisticsSection: View {
    var body: some View {
        HStack(spacing: 0) {
            ProfileStatisticItem(
                value: "36",
                title: L.Profile.totalAnimals
            )
            
            // 分隔线
            Rectangle()
                .fill(Color.black.opacity(0.05))
                .frame(width: 1, height: 40)
            
            ProfileStatisticItem(
                value: "24",
                title: L.Profile.animalsInstock
            )
            
            // 分隔线
            Rectangle()
                .fill(Color.black.opacity(0.05))
                .frame(width: 1, height: 40)
            
            ProfileStatisticItem(
                value: "12",
                title: L.Profile.animalsAdded
            )
        }
        .padding(.vertical, 20)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.08), lineWidth: 1)
        )
    }
}

// MARK: - 个人中心统计项目
struct ProfileStatisticItem: View {
    let value: String
    let title: String
    
    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.black)
            
            Text(title)
                .font(.system(size: 12))
                .foregroundColor(Color.black.opacity(0.6))
                .multilineTextAlignment(.center)
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 菜单区块
struct MenuSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.black)
            
            content
                .background(Color.white)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.black.opacity(0.08), lineWidth: 1)
                )
        }
    }
}

// MARK: - 菜单行
struct MenuRow: View {
    let icon: String
    let iconColor: Color
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标背景
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 44, height: 44)
                    .background(iconColor.opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
    }
}

// MARK: - 菜单行（简化版 - 只有单行文本）
struct MenuRowSingle: View {
    let icon: String
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 图标背景
                ZStack {
                    Circle()
                        .fill(Color.black.opacity(0.04))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: icon)
                        .font(.system(size: 18))
                        .foregroundColor(.black)
                }
                
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.black)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.3))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 14)
        }
    }
}

// MARK: - 邮箱弹窗
struct EmailPopupView: View {
    let emailAddress: String
    @Binding var isPresented: Bool
    @State private var isCopied = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text(L.Email.contact)
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.black)
                .padding(.top, 16)
            
            // 邮箱地址
            Text(emailAddress)
                .font(.system(size: 16))
                .foregroundColor(.black)
                .padding(.horizontal, 20)
                .padding(.bottom, 5)
            
            // 复制按钮
            Button(action: {
                UIPasteboard.general.string = emailAddress
                isCopied = true
                
                // 2秒后重置复制状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                    isCopied = false
                }
            }) {
                HStack {
                    Image(systemName: isCopied ? "checkmark" : "doc.on.doc")
                    Text(isCopied ? L.Email.copied : L.Email.copy)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.black)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .padding(.horizontal, 20)
            
            // 关闭按钮
            Button(action: {
                isPresented = false
            }) {
                Text(L.Email.close)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.black.opacity(0.06))
                    .foregroundColor(.black)
                    .cornerRadius(8)
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
        }
        .frame(width: UIScreen.main.bounds.width - 80)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.1), lineWidth: 1)
        )
    }
}

// MARK: - 使用教程视图
struct UsageGuideView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    ForEach(1...6, id: \.self) { index in
                        let content = switch index {
                        case 1: L.UsageGuide.content1
                        case 2: L.UsageGuide.content2
                        case 3: L.UsageGuide.content3
                        case 4: L.UsageGuide.content4
                        case 5: L.UsageGuide.content5
                        case 6: L.UsageGuide.content6
                        default: ""
                        }
                        
                        Text(content)
                            .font(.system(size: 16))
                            .foregroundColor(.black)
                            .lineSpacing(6)
                            .fixedSize(horizontal: false, vertical: true)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding(20)
                .frame(maxWidth: .infinity)
            }
            .background(Color(hex: "#F8F8F8"))
            .navigationBarTitleDisplayMode(.inline)
            .navigationTitle(L.UsageGuide.title)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(L.UsageGuide.close) {
                        dismiss()
                    }
                    .foregroundColor(.black)
                }
            }
        }
    }
}

#Preview {
    ProfileView()
}