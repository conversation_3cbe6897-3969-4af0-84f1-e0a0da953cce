# 牲畜管理应用 (LivestockManager)

一个现代化的iOS牲畜管理应用，帮助养殖户数字化管理牲畜信息。

## 功能特性

### 🏠 首页界面
- **统计概览**: 实时显示存栏、待售、已售、死亡牲畜数量
- **牲畜列表**: 卡片式展示所有牲畜信息
- **搜索筛选**: 快速查找和筛选牲畜
- **现代化UI**: 符合iOS设计规范的界面

### 📊 数据管理
- **牲畜档案**: 完整的牲畜信息记录
  - 唯一标识（耳标号）
  - 物种、品种、性别
  - 出生日期、年龄自动计算
  - 状态管理（存栏/待售/已售/死亡）
  - 备注信息

### 🎨 界面设计
- **统计卡片**: 清晰的数据展示
- **牲畜卡片**: 信息丰富的列表项
- **状态标签**: 颜色区分不同状态
- **底部导航**: 便捷的功能切换

## 技术栈

- **框架**: SwiftUI
- **架构**: MVVM
- **数据管理**: ObservableObject
- **UI组件**: 自定义组件库
- **图片加载**: AsyncImage

## 项目结构

```
LivestockManager/
├── Models.swift              # 数据模型
├── LivestockDataManager.swift # 数据管理器
├── ContentView.swift         # 主视图
├── StatisticsCardView.swift  # 统计卡片组件
├── LivestockCardView.swift   # 牲畜卡片组件
├── BottomTabView.swift       # 底部导航组件
└── Assets.xcassets          # 资源文件
```

## 运行方法

### 前提条件
- macOS 12.0+
- Xcode 14.0+
- iOS 15.0+ 模拟器或设备

### 编译运行
1. 克隆项目到本地
2. 使用Xcode打开 `LivestockManager.xcodeproj`
3. 选择目标设备（iPhone模拟器）
4. 点击运行按钮或按 `Cmd+R`

### 命令行编译
```bash
# 编译项目
xcodebuild -scheme LivestockManager -destination 'platform=iOS Simulator,name=iPhone 16' build

# 清理项目
xcodebuild clean
```

## 示例数据

应用内置了丰富的示例数据：
- **24只存栏牲畜**: 包含牛、羊、猪等不同物种
- **3只待售牲畜**: 准备出售的牲畜
- **8只已售牲畜**: 历史销售记录
- **1只死亡牲畜**: 死亡记录

## 界面截图

### 首页统计
- 顶部显示"我的牧场"标题
- 统计卡片显示各状态数量
- 搜索和筛选按钮

### 牲畜列表
- 卡片式布局
- 显示牲畜照片、编号、基本信息
- 状态标签颜色区分
- 点击查看详情

### 底部导航
- 牲畜管理（当前页面）
- 添加牲畜（绿色圆形按钮）
- 个人中心

## 开发计划

### V1.0 (当前版本)
- ✅ 牲畜档案管理
- ✅ 统计数据展示
- ✅ 搜索筛选功能
- ✅ 现代化UI设计

### V1.1 (计划中)
- 🔄 添加牲畜功能
- 🔄 编辑牲畜信息
- 🔄 删除牲畜记录
- 🔄 搜索功能实现

### V2.0 (未来版本)
- 📱 健康记录管理
- 📊 数据统计报表
- 🔄 繁殖记录追踪
- ☁️ 云端数据同步

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件

---

**注意**: 这是一个演示项目，展示了现代iOS应用开发的最佳实践。 